#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool

def debug_real_pages_content():
    """调试真实的pages_content数据结构"""
    
    processor = DocumentProcessor()
    
    # 测试文件路径
    test_file = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 开始调试真实的pages_content数据结构...")
    
    try:
        # 直接调用_extract_pages_content方法
        word_pool = WordInstancePool()
        
        with word_pool.get_instance() as word_instance:
            doc = word_instance.word_app.Documents.Open(test_file)
            
            try:
                # 提取pages_content
                print("📂 提取pages_content...")
                pages_content = processor._extract_pages_content(doc)
                
                # 检查第2页的内容
                print(f"\n📋 第2页真实内容结构:")
                page_2_data = pages_content.get(2, {})
                
                paragraphs = page_2_data.get('paragraphs', [])
                tables = page_2_data.get('tables', [])
                
                print(f"   第2页段落数: {len(paragraphs)}")
                print(f"   第2页表格数: {len(tables)}")
                
                # 合并并排序
                all_page2_content = paragraphs + tables
                all_page2_content.sort(key=lambda x: x.get('index', 0))
                
                print(f"   第2页总内容项数: {len(all_page2_content)}")
                
                # 显示索引范围
                if all_page2_content:
                    min_index = min(item.get('index', 0) for item in all_page2_content)
                    max_index = max(item.get('index', 0) for item in all_page2_content)
                    print(f"   索引范围: {min_index} - {max_index}")
                
                # 显示任务书相关的内容
                print(f"\n🔍 任务书相关内容 (索引23-84):")
                task_related_content = [item for item in all_page2_content if 23 <= item.get('index', 0) <= 84]
                print(f"   任务书范围内容项数: {len(task_related_content)}")
                
                for i, item in enumerate(task_related_content[:10]):  # 只显示前10项
                    index = item.get('index', 0)
                    text = item.get('text', '')[:50]
                    is_in_table = item.get('is_in_table', False)
                    item_type = 'table' if is_in_table else 'paragraph'
                    print(f"     {i+1:2d}. 索引{index:3d} [{item_type:8s}] | {text}...")
                
                if len(task_related_content) > 10:
                    print(f"     ... 还有 {len(task_related_content) - 10} 项内容")
                
                # 测试边界提取方法
                print(f"\n🔍 测试边界提取方法:")
                
                # 调用边界提取方法
                result_content = processor._extract_content_between_boundaries(
                    pages_content, 2, 23, 2, 84
                )
                
                print(f"   边界提取结果:")
                print(f"     文本长度: {len(result_content.get('text', ''))}")
                print(f"     段落数: {result_content.get('paragraph_count', 0)}")
                print(f"     字数: {result_content.get('word_count', 0)}")
                
                if result_content.get('text'):
                    text_preview = result_content['text'][:200]
                    print(f"     文本预览: {text_preview}...")
                else:
                    print(f"     ❌ 提取的文本为空")
                    
                    # 调试为什么为空
                    print(f"\n🔍 调试为什么提取为空:")
                    page_data = pages_content.get(2, {})
                    paragraphs_2 = page_data.get('paragraphs', [])
                    tables_2 = page_data.get('tables', [])
                    all_content_2 = paragraphs_2 + tables_2
                    all_content_2.sort(key=lambda x: x.get('index', 0))
                    
                    print(f"     第2页内容项数: {len(all_content_2)}")
                    
                    # 检查索引范围
                    matching_items = []
                    for item in all_content_2:
                        item_index = item.get('index', 0)
                        if 23 <= item_index <= 84:
                            matching_items.append(item)
                    
                    print(f"     索引23-84范围内的项数: {len(matching_items)}")
                    
                    if matching_items:
                        print(f"     匹配项示例:")
                        for i, item in enumerate(matching_items[:5]):
                            index = item.get('index', 0)
                            text = item.get('text', '')[:30]
                            print(f"       {i+1}. 索引{index} | {text}...")
                    else:
                        print(f"     ❌ 没有找到匹配的项")
                        print(f"     所有项的索引:")
                        for i, item in enumerate(all_content_2[:10]):
                            index = item.get('index', 0)
                            text = item.get('text', '')[:30]
                            print(f"       {i+1}. 索引{index} | {text}...")
                
                # 测试任务书结构检测
                print(f"\n🔍 测试任务书结构检测:")
                
                # 加载规则
                rules = processor._load_structure_rules()
                
                # 检测结构
                detected_structures = processor._detect_structures_by_page_order(pages_content, rules)
                
                # 找到任务书结构
                task_book_structure = None
                for structure in detected_structures:
                    if structure.get('name') == '任务书':
                        task_book_structure = structure
                        break
                
                if task_book_structure:
                    print(f"   任务书结构检测成功:")
                    print(f"     名称: {task_book_structure.get('name')}")
                    print(f"     页面: 第{task_book_structure.get('page')}页")
                    print(f"     状态: {task_book_structure.get('status')}")
                    print(f"     count: {task_book_structure.get('count')}")
                    
                    content = task_book_structure.get('content', {})
                    print(f"     段落索引: {content.get('paragraph_index')}")
                    print(f"     文本长度: {len(content.get('text', ''))}")
                    
                    if content.get('text'):
                        text_preview = content['text'][:100]
                        print(f"     文本预览: {text_preview}...")
                else:
                    print(f"   ❌ 未找到任务书结构")
                
            finally:
                # 关闭文档
                doc.Close()
                
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_real_pages_content()
