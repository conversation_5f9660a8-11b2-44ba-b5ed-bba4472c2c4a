#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def debug_boundary_calculation():
    """调试边界计算"""
    
    processor = DocumentProcessor()
    
    # 测试文件路径
    test_file = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 开始调试边界计算...")
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(test_file)
        
        # 检查任务书结构的边界计算
        if 'document_structures' in result:
            structures = result['document_structures']
            
            # 找到任务书结构
            task_book_index = None
            for i, structure in enumerate(structures):
                if structure.get('name') == '任务书':
                    task_book_index = i
                    break
            
            if task_book_index is not None:
                task_book = structures[task_book_index]
                
                print(f"\n📋 任务书结构信息:")
                print(f"   索引: {task_book_index}")
                print(f"   名称: {task_book.get('name')}")
                print(f"   页面: 第{task_book.get('page')}页")
                
                content = task_book.get('content', {})
                start_paragraph_index = content.get('paragraph_index', 0)
                print(f"   开始段落索引: {start_paragraph_index}")
                
                # 模拟边界计算
                print(f"\n🔍 模拟边界计算:")
                
                # 按页面和段落索引排序所有结构
                sorted_structures = sorted(structures, key=lambda x: (
                    x.get('page', 0),
                    x.get('content', {}).get('paragraph_index', 0)
                ))
                
                print(f"   排序后的结构:")
                for i, s in enumerate(sorted_structures):
                    name = s.get('name', '')
                    page = s.get('page', 'N/A')
                    para_idx = s.get('content', {}).get('paragraph_index', 'N/A')
                    marker = "⭐" if name == '任务书' else "  "
                    print(f"   {marker} {i:2d}. {name:30s} | 第{page}页 | 段落{para_idx}")
                
                # 找到任务书在排序后列表中的位置
                sorted_task_book_index = None
                for i, s in enumerate(sorted_structures):
                    if s.get('name') == '任务书':
                        sorted_task_book_index = i
                        break
                
                if sorted_task_book_index is not None:
                    print(f"\n   任务书在排序后列表中的索引: {sorted_task_book_index}")
                    
                    # 查找下一个结构
                    next_structure = None
                    for i in range(sorted_task_book_index + 1, len(sorted_structures)):
                        candidate = sorted_structures[i]
                        if candidate.get('status') == 'present':
                            next_structure = candidate
                            break
                    
                    if next_structure:
                        next_name = next_structure.get('name')
                        next_page = next_structure.get('page')
                        next_paragraph_index = next_structure.get('content', {}).get('paragraph_index', 0)
                        
                        print(f"   下一个结构: {next_name}")
                        print(f"   下一个结构页面: 第{next_page}页")
                        print(f"   下一个结构段落索引: {next_paragraph_index}")
                        
                        # 计算边界
                        current_page = task_book.get('page')
                        if next_page == current_page and next_paragraph_index > 0:
                            end_page = next_page
                            end_paragraph_index = next_paragraph_index - 1
                            print(f"   边界计算 (同页): 结束于第{end_page}页, 段落{end_paragraph_index}")
                        elif next_page > current_page:
                            # 跨页面的情况，返回当前页面的最后一个段落
                            print(f"   边界计算 (跨页): 当前页第{current_page}页到下一页第{next_page}页")
                            
                            # 检查当前页面的段落数
                            if 'content' in result:
                                paragraphs = result['content'].get('paragraphs', [])
                                current_page_paragraphs = [p for p in paragraphs if p.get('page_number') == current_page]
                                print(f"   当前页段落数: {len(current_page_paragraphs)}")
                                
                                if current_page_paragraphs:
                                    max_index = max(p.get('index', 0) for p in current_page_paragraphs)
                                    end_page = current_page
                                    end_paragraph_index = max_index
                                    print(f"   边界计算结果: 结束于第{end_page}页, 段落{end_paragraph_index}")
                                else:
                                    end_page = current_page
                                    end_paragraph_index = start_paragraph_index
                                    print(f"   边界计算结果 (无段落): 结束于第{end_page}页, 段落{end_paragraph_index}")
                        else:
                            end_page = next_page
                            end_paragraph_index = max(0, next_paragraph_index - 1)
                            print(f"   边界计算 (其他): 结束于第{end_page}页, 段落{end_paragraph_index}")
                        
                        # 模拟内容提取
                        print(f"\n🔍 模拟内容提取:")
                        print(f"   从第{current_page}页段落{start_paragraph_index} 到 第{end_page}页段落{end_paragraph_index}")
                        
                        # 检查这个范围内的内容
                        if 'content' in result:
                            paragraphs = result['content'].get('paragraphs', [])
                            tables = result['content'].get('tables', [])
                            
                            # 合并段落和表格
                            all_content = []
                            for p in paragraphs:
                                all_content.append({
                                    'type': 'paragraph',
                                    'index': p.get('index', 0),
                                    'page_number': p.get('page_number', 0),
                                    'text': p.get('text', ''),
                                    'is_in_table': p.get('is_in_table', False)
                                })
                            
                            for t in tables:
                                all_content.append({
                                    'type': 'table',
                                    'index': t.get('index', 0),
                                    'page_number': t.get('page_number', 0),
                                    'text': t.get('text', '')
                                })
                            
                            all_content.sort(key=lambda x: x['index'])
                            
                            # 筛选范围内的内容
                            range_content = []
                            for item in all_content:
                                item_index = item['index']
                                item_page = item['page_number']
                                
                                # 检查是否在范围内
                                if current_page == end_page:
                                    # 同页面
                                    if item_page == current_page and start_paragraph_index <= item_index <= end_paragraph_index:
                                        range_content.append(item)
                                else:
                                    # 跨页面
                                    if (item_page == current_page and item_index >= start_paragraph_index) or \
                                       (item_page > current_page and item_page < end_page) or \
                                       (item_page == end_page and item_index <= end_paragraph_index):
                                        range_content.append(item)
                            
                            print(f"   范围内内容项数: {len(range_content)}")
                            print(f"   内容详情:")
                            for i, item in enumerate(range_content[:10]):  # 只显示前10项
                                item_type = item['type']
                                text = item['text'][:50]
                                index = item['index']
                                page = item['page_number']
                                is_in_table = item.get('is_in_table', False)
                                
                                table_marker = "[表格]" if item_type == 'table' or is_in_table else "[段落]"
                                print(f"     {i+1:2d}. 索引{index:3d} 第{page}页 {table_marker} | {text}...")
                            
                            if len(range_content) > 10:
                                print(f"     ... 还有 {len(range_content) - 10} 项内容")
                            
                            # 计算字数
                            total_text = ' '.join(item['text'] for item in range_content)
                            word_count = len(total_text.replace(' ', '').replace('\n', '').replace('\r', '').replace('\t', ''))
                            print(f"   总字数: {word_count}")
                    else:
                        print(f"   没有找到下一个结构")
                else:
                    print(f"   在排序后列表中未找到任务书")
            else:
                print("❌ 未找到任务书结构")
        else:
            print("❌ 结果中没有document_structures字段")
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_boundary_calculation()
