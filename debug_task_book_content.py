#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def debug_task_book_content():
    """调试任务书内容提取"""
    
    processor = DocumentProcessor()
    
    # 测试文件路径
    test_file = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 开始调试任务书内容提取...")
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(test_file)
        
        # 检查任务书结构
        if 'document_structures' in result:
            structures = result['document_structures']
            
            # 找到任务书结构
            task_book = None
            for structure in structures:
                if structure.get('name') == '任务书':
                    task_book = structure
                    break
            
            if task_book:
                print(f"\n📋 任务书结构信息:")
                print(f"   名称: {task_book.get('name')}")
                print(f"   页面: 第{task_book.get('page')}页")
                print(f"   状态: {task_book.get('status')}")
                print(f"   类型: {task_book.get('type')}")
                
                content = task_book.get('content', {})
                print(f"\n📝 任务书内容信息:")
                print(f"   段落索引: {content.get('paragraph_index')}")
                print(f"   文本长度: {len(content.get('text', ''))}")
                print(f"   样式: {content.get('style')}")
                print(f"   对齐: {content.get('alignment')}")
                
                # 检查完整内容
                complete_text = content.get('complete_text', '')
                word_count = content.get('word_count', 0)
                paragraph_count = content.get('paragraph_count', 0)
                
                print(f"\n🔍 完整内容信息:")
                print(f"   完整文本长度: {len(complete_text)}")
                print(f"   字数统计: {word_count}")
                print(f"   段落数: {paragraph_count}")
                
                if complete_text:
                    print(f"   完整内容预览: {complete_text[:500]}...")
                else:
                    print(f"   ❌ 完整内容为空")
                
                # 检查原始文本内容
                original_text = content.get('text', '')
                if original_text:
                    print(f"\n📄 原始文本内容:")
                    print(f"   原始文本: {original_text[:200]}...")
                
                # 检查count字段
                count = task_book.get('count', '')
                print(f"\n📊 统计信息:")
                print(f"   count字段: {count}")
                
            else:
                print("❌ 未找到任务书结构")
        else:
            print("❌ 结果中没有document_structures字段")
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_task_book_content()
