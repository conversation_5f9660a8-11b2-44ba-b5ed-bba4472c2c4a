"""
文档处理器

提供Word文档的处理功能：
- 文档打开、关闭和基本操作
- 文档内容提取和解析
- 文档信息获取
- 集成重试机制和线程安全
"""

import os
import time
import threading
import re
import json
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime
from pathlib import Path
import structlog
from contextlib import contextmanager
from functools import lru_cache

from app.services.word_com import get_word_application, WordCOMError
from app.core.resource_manager import get_word_pool
from app.core.retry import with_retry, WORD_OPERATION_RETRY_CONFIG
from app.core.threading import com_thread_safe
from app.core.config import settings
from dataclasses import dataclass, field

logger = structlog.get_logger()

# 常量定义
MAX_FILE_SIZE_MB = 50  # 最大文件大小（MB）
MAX_CACHE_SIZE = 100   # 最大缓存项数
FIRST_PAGE_MAX_CHARS = 3000  # 第一页最大字符数
FIRST_PAGE_MAX_PARAGRAPHS = 50  # 第一页最大段落数
COVER_SIMILARITY_THRESHOLD = 0.85  # 封面相似度阈值

# 预编译正则表达式以提高性能
COMPILED_PATTERNS = {
    'chinese_date': [
        re.compile(r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日'),
        re.compile(r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月'),
        re.compile(r'(\d{4})年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日'),
        re.compile(r'(\d{4})年(\d{1,2})月(\d{1,2})日'),
        re.compile(r'(\d{4})年(\d{1,2})月'),
        re.compile(r'(\d{4})\s+年\s+(\d{1,2})\s*月\s+(\d{1,2})\s*日'),
        re.compile(r'(\d{4})\s+年\s+(\d{1,2})\s*月'),
    ],
    'chapter': [
        re.compile(r'^第[一二三四五六七八九十\d]+章'),
        re.compile(r'^\d+\.?\s*[^\d]'),
        re.compile(r'^[一二三四五六七八九十]+[、．.]'),
        re.compile(r'^\d+\.\d+'),
    ],
    'table_figure': [
        re.compile(r'^表\d+\.?\d*'),
        re.compile(r'^Table\s*\d+\.?\d*', re.IGNORECASE),
        re.compile(r'^表格\d+\.?\d*'),
        re.compile(r'^续表\d+\.?\d*'),
        re.compile(r'^图\d+\.?\d*'),
        re.compile(r'^Figure\s*\d+\.?\d*', re.IGNORECASE),
        re.compile(r'^Fig\.\s*\d+\.?\d*', re.IGNORECASE),
        re.compile(r'^图片\d+\.?\d*'),
    ],
    'chinese_chars': re.compile(r'[\u4e00-\u9fff]+'),
    'english_chars': re.compile(r'[a-zA-Z]+'),
    'control_chars': re.compile(r'[\u0000-\u001F\u007F-\u009F]'),
}


@dataclass
class DocumentData:
    """封装文档所有分析数据，用于传递给规则引擎。"""
    file_path: str
    doc_info: Dict[str, Any] = field(default_factory=dict)
    content_stats: Dict[str, Any] = field(default_factory=dict)
    elements: List[Dict[str, Any]] = field(default_factory=list)
    paragraphs: List[Dict[str, Any]] = field(default_factory=list)
    tables: List[Dict[str, Any]] = field(default_factory=list)
    images: List[Dict[str, Any]] = field(default_factory=list)


class DocumentProcessorError(Exception):
    """
    文档处理器异常

    用于封装文档处理过程中的各种错误，提供更好的错误信息和调试支持。
    """

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        """
        初始化异常

        Args:
            message: 错误消息
            original_error: 原始异常（如果有）
        """
        super().__init__(message)
        self.original_error = original_error

    def __str__(self) -> str:
        if self.original_error:
            return f"{super().__str__()} (原因: {str(self.original_error)})"
        return super().__str__()


class ChineseDateParser:
    """
    中文日期解析器

    提供中文日期格式的解析功能，支持多种中文日期表示方式：
    - 完整格式：二〇二五年四月二十日
    - 年月格式：二〇二五年四月
    - 混合格式：2025年四月二十日
    - 纯数字格式：2025年4月20日
    """

    # 中文数字映射
    CHINESE_NUMBER_MAP: Dict[str, int] = {
        '零': 0, '○': 0, '〇': 0,
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
        '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
        '廿': 20, '卅': 30
    }

    # 中文月份映射
    CHINESE_MONTH_MAP: Dict[str, int] = {
        '正': 1, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6,
        '七': 7, '八': 8, '九': 9, '十': 10, '冬': 11, '腊': 12
    }
    
    @classmethod
    def chinese_to_arabic(cls, chinese_str: str) -> int:
        """
        将中文数字转换为阿拉伯数字
        
        Args:
            chinese_str: 中文数字字符串
            
        Returns:
            int: 阿拉伯数字
        """
        if not chinese_str:
            return 0
            
        # 如果已经是阿拉伯数字，直接返回
        if chinese_str.isdigit():
            return int(chinese_str)
        
        # 特殊处理年份格式（如：二〇二五、二○二四等）
        if len(chinese_str) == 4 and all(c in cls.CHINESE_NUMBER_MAP for c in chinese_str):
            # 检查是否是年份格式（每个字符都是0-9的中文数字）
            year_digits = []
            for char in chinese_str:
                if char in cls.CHINESE_NUMBER_MAP:
                    digit = cls.CHINESE_NUMBER_MAP[char]
                    if digit <= 9:  # 只处理0-9的数字
                        year_digits.append(str(digit))
                    else:
                        # 不是年份格式，使用常规解析
                        break
            
            if len(year_digits) == 4:
                # 是年份格式，直接拼接
                return int(''.join(year_digits))
        
        # 常规中文数字转换
        result = 0
        temp_result = 0
        
        for char in chinese_str:
            if char in cls.CHINESE_NUMBER_MAP:
                num = cls.CHINESE_NUMBER_MAP[char]
                
                if num == 10:
                    # 处理十的情况
                    if temp_result == 0:
                        temp_result = 10
                    else:
                        temp_result *= 10
                elif num < 10:
                    # 处理个位数
                    temp_result += num
                else:
                    # 处理更大的数字单位
                    if temp_result == 0:
                        temp_result = 1
                    result += temp_result * num
                    temp_result = 0
        
        return result + temp_result
    
    @classmethod
    def parse_chinese_date(cls, date_str: str) -> Optional[str]:
        """
        解析中文日期格式

        Args:
            date_str: 中文日期字符串

        Returns:
            Optional[str]: 格式化后的日期字符串，如 "2025年4月20日"
        """
        if not date_str:
            return None

        logger.info(f"解析中文日期: {date_str}")

        # 使用预编译的正则表达式
        for pattern in COMPILED_PATTERNS['chinese_date']:
            match = pattern.search(date_str)
            if match:
                try:
                    groups = match.groups()
                    logger.info(f"匹配到日期模式: {pattern}, 组: {groups}")
                    
                    # 提取年
                    year_str = groups[0]
                    if year_str.isdigit():
                        year = int(year_str)
                    else:
                        year = cls.chinese_to_arabic(year_str)
                        # 处理简化年份（如二五年表示2025年）
                        if year < 100:
                            year += 2000
                    
                    # 提取月
                    month_str = groups[1]
                    if month_str.isdigit():
                        month = int(month_str)
                    else:
                        # 优先使用特殊月份映射
                        if month_str in cls.CHINESE_MONTH_MAP:
                            month = cls.CHINESE_MONTH_MAP[month_str]
                        else:
                            month = cls.chinese_to_arabic(month_str)
                    
                    # 提取日（如果有）
                    day = 1  # 默认为1日
                    if len(groups) > 2 and groups[2]:
                        day_str = groups[2]
                        if day_str.isdigit():
                            day = int(day_str)
                        else:
                            day = cls.chinese_to_arabic(day_str)
                    
                    # 验证日期有效性
                    if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                        # 返回格式化的日期字符串
                        if len(groups) > 2 and groups[2]:
                            result = f"{year}年{month}月{day}日"
                        else:
                            result = f"{year}年{month}月"
                        
                        logger.info(f"成功解析中文日期: {date_str} -> {result}")
                        return result
                    else:
                        logger.warning(f"日期值超出有效范围: {year}年{month}月{day}日")
                        
                except Exception as e:
                    logger.error(f"解析中文日期失败: {date_str}, 错误: {str(e)}")
                    continue
        
        logger.warning(f"无法解析中文日期: {date_str}")
        return None


class DocumentProcessor:
    """文档处理器"""

    def __init__(self, use_pool: bool = True):
        """
        初始化文档处理器

        Args:
            use_pool: 是否使用Word实例池
        """
        self.use_pool = use_pool
        self.word_pool = get_word_pool() if use_pool else None
        self.processing_stats = {
            'documents_processed': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_processing_time': 0.0
        }
        self.date_parser = ChineseDateParser()
        # 添加缓存以提高性能
        self._file_info_cache = {}
        self._structure_rules_cache = None

        # 🚀 性能优化：添加文档分析结果缓存
        self._analysis_cache = {}
        self._cache_max_size = 50  # 最多缓存50个文档的分析结果

    def _get_file_cache_key(self, file_path: str) -> str:
        """生成文件缓存键"""
        import os
        import hashlib

        # 使用文件路径、大小和修改时间生成缓存键
        try:
            stat = os.stat(file_path)
            cache_data = f"{file_path}_{stat.st_size}_{stat.st_mtime}"
            return hashlib.md5(cache_data.encode()).hexdigest()
        except:
            return hashlib.md5(file_path.encode()).hexdigest()

    def _get_cached_analysis(self, file_path: str) -> Optional[Dict[str, Any]]:
        """获取缓存的分析结果"""
        cache_key = self._get_file_cache_key(file_path)
        return self._analysis_cache.get(cache_key)

    def _cache_analysis_result(self, file_path: str, result: Dict[str, Any]):
        """缓存分析结果"""
        cache_key = self._get_file_cache_key(file_path)

        # 如果缓存已满，删除最旧的条目
        if len(self._analysis_cache) >= self._cache_max_size:
            oldest_key = next(iter(self._analysis_cache))
            del self._analysis_cache[oldest_key]

        self._analysis_cache[cache_key] = result
        logger.info(f"📦 缓存分析结果: {file_path} -> {cache_key[:8]}...")

    @contextmanager
    def _document_context(self, file_path: str, read_only: bool = True):
        """
        文档操作上下文管理器，统一处理文档打开/关闭逻辑

        Args:
            file_path: 文档文件路径
            read_only: 是否只读模式

        Yields:
            Word文档对象
        """
        start_time = time.time()
        operation_success = False
        doc = None
        word_app = None

        try:
            # 验证文件
            self._validate_file(file_path)

            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=read_only)
                    try:
                        yield doc
                        operation_success = True
                    finally:
                        if doc:
                            try:
                                word_app.close_document(doc)
                            except Exception as close_error:
                                logger.warning(f"关闭文档失败: {close_error}")
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=read_only)
                try:
                    yield doc
                    operation_success = True
                finally:
                    if doc and word_app:
                        try:
                            word_app.close_document(doc)
                        except Exception as close_error:
                            logger.warning(f"关闭文档失败: {close_error}")

        except DocumentProcessorError:
            # 重新抛出已经包装的异常
            raise
        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"文档操作失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"文档操作失败: {str(e)}")

        finally:
            processing_time = time.time() - start_time
            self.processing_stats['total_processing_time'] += processing_time
            if operation_success:
                self.processing_stats['successful_operations'] += 1
            logger.info(f"文档操作完成: {file_path}, 耗时: {processing_time:.2f}秒, 成功: {operation_success}")

    def _update_document_processed_stats(self):
        """更新文档处理统计"""
        self.processing_stats['documents_processed'] += 1
    
    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def get_document_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文档基本信息

        Args:
            file_path: 文档文件路径

        Returns:
            dict: 文档信息
        """
        logger.info(f"开始获取文档信息: {file_path}")

        # 直接使用原有逻辑，因为上下文管理器已经处理了Word应用程序
        try:
            # 验证文件
            self._validate_file(file_path)

            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=True)
                    try:
                        info = word_app.get_document_info(doc)
                        # 添加文件系统信息
                        info.update(self._get_file_system_info(file_path))
                        self.processing_stats['successful_operations'] += 1
                        return info
                    finally:
                        word_app.close_document(doc)
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    info = word_app.get_document_info(doc)
                    # 添加文件系统信息
                    info.update(self._get_file_system_info(file_path))
                    self.processing_stats['successful_operations'] += 1
                    return info
                finally:
                    word_app.close_document(doc)

        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"获取文档信息失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"获取文档信息失败: {str(e)}")
    
    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def extract_document_content(self, file_path: str, include_formatting: bool = False) -> Dict[str, Any]:
        """
        提取文档内容

        Args:
            file_path: 文档文件路径
            include_formatting: 是否包含格式信息

        Returns:
            dict: 文档内容
        """
        logger.info(f"开始提取文档内容: {file_path}")

        try:
            # 验证文件
            self._validate_file(file_path)

            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=True)
                    try:
                        content = self._extract_content_from_document(doc, include_formatting)
                        self.processing_stats['successful_operations'] += 1
                        self._update_document_processed_stats()
                        return content
                    finally:
                        word_app.close_document(doc)
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    content = self._extract_content_from_document(doc, include_formatting)
                    self.processing_stats['successful_operations'] += 1
                    self._update_document_processed_stats()
                    return content
                finally:
                    word_app.close_document(doc)

        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"提取文档内容失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"提取文档内容失败: {str(e)}")
    
    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def analyze_document_comprehensive(self, file_path: str, include_formatting: bool = False) -> Dict[str, Any]:
        """
        一次性完成文档内容提取和结构分析（性能优化版本）

        Args:
            file_path: 文档文件路径
            include_formatting: 是否包含格式信息

        Returns:
            dict: 包含内容和结构的完整分析结果
        """
        import time
        start_time = time.time()
        logger.info(f"🚀 开始综合分析文档: {file_path}")

        # 🚀 性能优化：检查缓存
        cached_result = self._get_cached_analysis(file_path)
        if cached_result:
            cache_time = time.time() - start_time
            logger.info(f"🎯 使用缓存结果，耗时: {cache_time:.3f}秒")
            return cached_result

        try:
            # 验证文件
            validate_start = time.time()
            self._validate_file(file_path)
            validate_time = time.time() - validate_start
            logger.info(f"⏱️ 文件验证耗时: {validate_time:.3f}秒")

            if self.use_pool and self.word_pool:
                # 使用实例池
                pool_start = time.time()
                with self.word_pool.get_instance() as word_app:
                    pool_time = time.time() - pool_start
                    logger.info(f"⏱️ 获取Word实例耗时: {pool_time:.3f}秒")

                    open_start = time.time()
                    doc = word_app.open_document(file_path, read_only=True)
                    open_time = time.time() - open_start
                    logger.info(f"⏱️ 打开文档耗时: {open_time:.3f}秒")

                    try:
                        # 一次性完成内容提取和结构分析
                        content_start = time.time()
                        content_result = self._extract_content_from_document(doc, include_formatting)
                        content_time = time.time() - content_start
                        logger.info(f"⏱️ 内容提取耗时: {content_time:.3f}秒")

                        structure_start = time.time()
                        structure_result = self._analyze_document_structure(doc)
                        structure_time = time.time() - structure_start
                        logger.info(f"⏱️ 结构分析耗时: {structure_time:.3f}秒")

                        # 合并结果
                        merge_start = time.time()
                        comprehensive_result = {
                            'content': content_result,
                            'structure': structure_result,
                            # 将结构分析的顶级字段提升到根级别，保持向后兼容
                            **structure_result
                        }
                        merge_time = time.time() - merge_start
                        logger.info(f"⏱️ 结果合并耗时: {merge_time:.3f}秒")

                        self.processing_stats['successful_operations'] += 1
                        total_time = time.time() - start_time
                        logger.info(f"⏱️ 综合分析总耗时: {total_time:.3f}秒")

                        # 🚀 性能优化：缓存分析结果
                        self._cache_analysis_result(file_path, comprehensive_result)

                        return comprehensive_result
                    finally:
                        close_start = time.time()
                        word_app.close_document(doc)
                        close_time = time.time() - close_start
                        logger.info(f"⏱️ 关闭文档耗时: {close_time:.3f}秒")
            else:
                # 直接使用Word应用程序
                app_start = time.time()
                word_app = get_word_application()
                app_time = time.time() - app_start
                logger.info(f"⏱️ 获取Word应用程序耗时: {app_time:.3f}秒")

                open_start = time.time()
                doc = word_app.open_document(file_path, read_only=True)
                open_time = time.time() - open_start
                logger.info(f"⏱️ 打开文档耗时: {open_time:.3f}秒")

                try:
                    # 一次性完成内容提取和结构分析
                    content_start = time.time()
                    content_result = self._extract_document_content(doc, include_formatting)
                    content_time = time.time() - content_start
                    logger.info(f"⏱️ 内容提取耗时: {content_time:.3f}秒")

                    structure_start = time.time()
                    structure_result = self._analyze_document_structure(doc)
                    structure_time = time.time() - structure_start
                    logger.info(f"⏱️ 结构分析耗时: {structure_time:.3f}秒")

                    # 合并结果
                    merge_start = time.time()
                    comprehensive_result = {
                        'content': content_result,
                        'structure': structure_result,
                        # 将结构分析的顶级字段提升到根级别，保持向后兼容
                        **structure_result
                    }
                    merge_time = time.time() - merge_start
                    logger.info(f"⏱️ 结果合并耗时: {merge_time:.3f}秒")

                    self.processing_stats['successful_operations'] += 1
                    total_time = time.time() - start_time
                    logger.info(f"⏱️ 综合分析总耗时: {total_time:.3f}秒")

                    # 🚀 性能优化：缓存分析结果
                    self._cache_analysis_result(file_path, comprehensive_result)

                    return comprehensive_result
                finally:
                    close_start = time.time()
                    word_app.close_document(doc)
                    close_time = time.time() - close_start
                    logger.info(f"⏱️ 关闭文档耗时: {close_time:.3f}秒")

        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            total_time = time.time() - start_time
            logger.error(f"❌ 综合分析文档失败: {file_path}, 错误: {str(e)}, 耗时: {total_time:.3f}秒")
            raise DocumentProcessorError(f"综合分析文档失败: {str(e)}")

    def extract_raw_document_data(self, file_path: str, progress_callback=None) -> Dict[str, Any]:
        """
        🔥 新方法：纯粹的原始数据提取（仅使用Word COM读取，不进行任何分析）

        Args:
            file_path: 文档文件路径
            progress_callback: 进度回调函数

        Returns:
            dict: 原始文档数据
        """
        import time
        start_time = time.time()
        logger.info(f"🔥 开始提取原始文档数据: {file_path}")

        try:
            # 验证文件
            validate_start = time.time()
            self._validate_file(file_path)
            validate_time = time.time() - validate_start
            logger.info(f"⏱️ 文件验证耗时: {validate_time:.3f}秒")

            if progress_callback:
                try:
                    progress_callback(18, "正在打开Word文档...")
                except Exception as e:
                    logger.warning(f"进度回调调用失败: {e}")

            raw_data = {}

            if self.use_pool and self.word_pool:
                # 使用实例池
                pool_start = time.time()
                with self.word_pool.get_instance() as word_app:
                    pool_time = time.time() - pool_start
                    logger.info(f"⏱️ 获取Word实例耗时: {pool_time:.3f}秒")

                    if progress_callback:
                        try:
                            progress_callback(22, "正在读取文档基本信息...")
                        except Exception as e:
                            logger.warning(f"进度回调调用失败: {e}")

                    open_start = time.time()
                    doc = word_app.open_document(file_path, read_only=True)
                    open_time = time.time() - open_start
                    logger.info(f"⏱️ 打开文档耗时: {open_time:.3f}秒")

                    try:
                        # 🔥 关键：只进行纯粹的数据读取，不进行任何分析

                        # 1. 读取文档基本信息
                        if progress_callback:
                            try:
                                progress_callback(25, "正在读取文档属性...")
                            except Exception as e:
                                logger.warning(f"进度回调调用失败: {e}")

                        info_start = time.time()
                        document_info = word_app.get_document_info(doc)
                        info_time = time.time() - info_start
                        logger.info(f"⏱️ 文档信息读取耗时: {info_time:.3f}秒")

                        # 2. 读取文档内容
                        if progress_callback:
                            try:
                                progress_callback(28, "正在读取文档内容...")
                            except Exception as e:
                                logger.warning(f"进度回调调用失败: {e}")

                        content_start = time.time()
                        document_content = self._extract_content_from_document(doc, True)
                        content_time = time.time() - content_start
                        logger.info(f"⏱️ 文档内容读取耗时: {content_time:.3f}秒")

                        # 3. 读取文档结构信息
                        if progress_callback:
                            try:
                                progress_callback(32, "正在读取文档结构...")
                            except Exception as e:
                                logger.warning(f"进度回调调用失败: {e}")

                        structure_start = time.time()
                        document_structure = self._analyze_document_structure(doc)
                        structure_time = time.time() - structure_start
                        logger.info(f"⏱️ 文档结构读取耗时: {structure_time:.3f}秒")

                        # 合并所有原始数据
                        raw_data = {
                            'document_info': document_info,
                            'document_content': document_content,
                            'document_structure': document_structure,
                            'file_path': file_path,
                            'extraction_time': time.time() - start_time,
                            'extraction_method': 'word_com_pool'
                        }

                        self.processing_stats['successful_operations'] += 1
                        total_time = time.time() - start_time
                        logger.info(f"⏱️ 原始数据提取总耗时: {total_time:.3f}秒")

                        return {
                            'success': True,
                            'raw_data': raw_data,
                            'processing_time': total_time
                        }

                    finally:
                        close_start = time.time()
                        word_app.close_document(doc)
                        close_time = time.time() - close_start
                        logger.info(f"⏱️ 关闭文档耗时: {close_time:.3f}秒")

                        if progress_callback:
                            try:
                                progress_callback(35, "Word文档已关闭，数据提取完成")
                            except Exception as e:
                                logger.warning(f"进度回调调用失败: {e}")
            else:
                # 直接使用Word应用程序
                logger.info("使用直接Word应用程序模式")
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    # 类似的数据提取逻辑
                    document_info = word_app.get_document_info(doc)
                    document_content = self._extract_content_from_document(doc, True)
                    document_structure = self._analyze_document_structure(doc)

                    raw_data = {
                        'document_info': document_info,
                        'document_content': document_content,
                        'document_structure': document_structure,
                        'file_path': file_path,
                        'extraction_time': time.time() - start_time,
                        'extraction_method': 'word_com_direct'
                    }

                    return {
                        'success': True,
                        'raw_data': raw_data,
                        'processing_time': time.time() - start_time
                    }
                finally:
                    word_app.close_document(doc)

        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            total_time = time.time() - start_time
            error_msg = f"原始数据提取失败: {str(e)}"
            logger.error(f"❌ {error_msg}, 耗时: {total_time:.3f}秒")

            return {
                'success': False,
                'error_message': error_msg,
                'processing_time': total_time
            }

    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def analyze_document_structure(self, file_path: str) -> Dict[str, Any]:
        """
        分析文档结构

        Args:
            file_path: 文档文件路径

        Returns:
            dict: 文档结构信息
        """
        logger.info(f"开始分析文档结构: {file_path}")

        try:
            # 验证文件
            self._validate_file(file_path)

            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=True)
                    try:
                        structure = self._analyze_document_structure(doc)
                        self.processing_stats['successful_operations'] += 1
                        return structure
                    finally:
                        word_app.close_document(doc)
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    structure = self._analyze_document_structure(doc)
                    self.processing_stats['successful_operations'] += 1
                    return structure
                finally:
                    word_app.close_document(doc)

        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"分析文档结构失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"分析文档结构失败: {str(e)}")
    
    def _validate_file(self, file_path: str):
        """验证文件"""
        if not os.path.exists(file_path):
            raise DocumentProcessorError(f"文件不存在: {file_path}")
        
        if not os.path.isfile(file_path):
            raise DocumentProcessorError(f"路径不是文件: {file_path}")
        
        # 检查文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in ['.doc', '.docx']:
            raise DocumentProcessorError(f"不支持的文件格式: {ext}")
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        max_size = getattr(settings, 'MAX_DOCUMENT_SIZE', MAX_FILE_SIZE_MB * 1024 * 1024)
        if file_size > max_size:
            raise DocumentProcessorError(f"文件过大: {file_size} bytes (最大: {max_size} bytes)")
    
    def _get_file_system_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件系统信息（带缓存）"""
        # 使用文件路径和修改时间作为缓存键
        try:
            stat = os.stat(file_path)
            cache_key = f"{file_path}_{stat.st_mtime}"

            # 检查缓存
            if cache_key in self._file_info_cache:
                return self._file_info_cache[cache_key]

            # 生成文件信息
            file_info = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size': stat.st_size,
                'file_extension': os.path.splitext(file_path)[1].lower(),
                'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed_time': datetime.fromtimestamp(stat.st_atime).isoformat()
            }

            # 缓存结果（限制缓存大小）
            if len(self._file_info_cache) > MAX_CACHE_SIZE:
                # 清理最旧的缓存项
                oldest_key = next(iter(self._file_info_cache))
                del self._file_info_cache[oldest_key]

            self._file_info_cache[cache_key] = file_info
            return file_info

        except Exception as e:
            logger.warning(f"获取文件系统信息失败: {str(e)}")
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_extension': os.path.splitext(file_path)[1].lower()
            }
    
    def _extract_content_from_document(self, doc: Any, include_formatting: bool = False) -> Dict[str, Any]:
        """从文档对象提取内容"""
        try:
            content = {
                'text': '',
                'paragraphs': [],
                'tables': [],
                'images': [],
                'headers_footers': {},
                'metadata': {}
            }
            
            # 提取文本内容
            try:
                content['text'] = doc.Content.Text
            except Exception as e:
                logger.warning(f"提取文档文本失败: {str(e)}")
            
            # 提取段落
            try:
                paragraphs = []
                for i, paragraph in enumerate(doc.Paragraphs):
                    para_info = {
                        'index': i + 1,
                        'text': paragraph.Range.Text.strip(),
                        'style': getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal'
                    }
                    
                    if include_formatting:
                        para_info.update({
                            'alignment': getattr(paragraph.Format, 'Alignment', None),
                            'first_line_indent': getattr(paragraph.Format, 'FirstLineIndent', 0),
                            'left_indent': getattr(paragraph.Format, 'LeftIndent', 0),
                            'right_indent': getattr(paragraph.Format, 'RightIndent', 0),
                            'space_before': getattr(paragraph.Format, 'SpaceBefore', 0),
                            'space_after': getattr(paragraph.Format, 'SpaceAfter', 0)
                        })
                    
                    if para_info['text']:  # 只添加非空段落
                        paragraphs.append(para_info)
                
                content['paragraphs'] = paragraphs
                
            except Exception as e:
                logger.warning(f"提取段落信息失败: {str(e)}")
            
            # 提取表格
            try:
                tables = []
                for i, table in enumerate(doc.Tables):
                    table_info = {
                        'index': i + 1,
                        'rows': table.Rows.Count,
                        'columns': table.Columns.Count,
                        'data': []
                    }
                    
                    # 提取表格数据
                    try:
                        for row_idx in range(1, table.Rows.Count + 1):
                            row_data = []
                            for col_idx in range(1, table.Columns.Count + 1):
                                try:
                                    cell_text = table.Cell(row_idx, col_idx).Range.Text.strip()
                                    # 移除Word表格单元格末尾的特殊字符
                                    cell_text = cell_text.replace('\r\x07', '').replace('\x07', '')
                                    row_data.append(cell_text)
                                except Exception:
                                    row_data.append('')
                            table_info['data'].append(row_data)
                    except Exception as e:
                        logger.warning(f"提取表格数据失败: {str(e)}")
                    
                    tables.append(table_info)
                
                content['tables'] = tables
                
            except Exception as e:
                logger.warning(f"提取表格信息失败: {str(e)}")
            
            # 提取图片信息
            try:
                images = []
                for i, shape in enumerate(doc.InlineShapes):
                    if hasattr(shape, 'Type') and shape.Type == 3:  # wdInlineShapePicture
                        image_info = {
                            'index': i + 1,
                            'width': getattr(shape, 'Width', 0),
                            'height': getattr(shape, 'Height', 0),
                            'type': 'inline_picture'
                        }
                        images.append(image_info)
                
                # 检查浮动图片
                for i, shape in enumerate(doc.Shapes):
                    if hasattr(shape, 'Type') and shape.Type == 13:  # msoShapeTypePicture
                        image_info = {
                            'index': len(images) + 1,
                            'width': getattr(shape, 'Width', 0),
                            'height': getattr(shape, 'Height', 0),
                            'type': 'floating_picture'
                        }
                        images.append(image_info)
                
                content['images'] = images
                
            except Exception as e:
                logger.warning(f"提取图片信息失败: {str(e)}")
            
            # 提取页眉页脚
            try:
                headers_footers = {}
                
                # 页眉
                try:
                    for section_idx, section in enumerate(doc.Sections):
                        headers = {}
                        headers['primary'] = section.Headers(1).Range.Text.strip() if section.Headers(1).Exists else ''
                        headers['first_page'] = section.Headers(2).Range.Text.strip() if section.Headers(2).Exists else ''
                        headers['even_pages'] = section.Headers(3).Range.Text.strip() if section.Headers(3).Exists else ''
                        headers_footers[f'section_{section_idx + 1}_headers'] = headers
                except Exception as e:
                    logger.warning(f"提取页眉失败: {str(e)}")
                
                # 页脚
                try:
                    for section_idx, section in enumerate(doc.Sections):
                        footers = {}
                        footers['primary'] = section.Footers(1).Range.Text.strip() if section.Footers(1).Exists else ''
                        footers['first_page'] = section.Footers(2).Range.Text.strip() if section.Footers(2).Exists else ''
                        footers['even_pages'] = section.Footers(3).Range.Text.strip() if section.Footers(3).Exists else ''
                        headers_footers[f'section_{section_idx + 1}_footers'] = footers
                except Exception as e:
                    logger.warning(f"提取页脚失败: {str(e)}")
                
                content['headers_footers'] = headers_footers
                
            except Exception as e:
                logger.warning(f"提取页眉页脚失败: {str(e)}")
            
            return content
            
        except Exception as e:
            logger.error(f"提取文档内容失败: {str(e)}")
            raise DocumentProcessorError(f"提取文档内容失败: {str(e)}")
    
    def _analyze_document_structure(self, doc: Any) -> Dict[str, Any]:
        """分析文档结构"""
        try:
            structure = {
                'outline': [],
                'styles_used': [],
                'sections': [],
                'toc_entries': [],
                'statistics': {},
                'cover_page_info': {}  # 新增：封面页信息
            }
            
            # 基于规则检测文档结构
            try:
                logger.info("🔥 调试：开始基于规则的结构分析")
                document_structure = self._detect_document_structure_by_rules(doc)
                structure.update(document_structure)
                logger.info(f"🔥 调试：基于规则的结构分析成功，生成了 {len(document_structure.get('document_structures', []))} 个结构")
            except Exception as e:
                logger.warning(f"🔥 调试：基于规则的结构分析失败: {str(e)}")
                import traceback
                logger.warning(f"🔥 调试：错误详情: {traceback.format_exc()}")
                # 使用备用方法
                logger.info("🔥 调试：使用备用结构检测方法")
                structure.update(self._fallback_structure_detection(doc))
            
            # 统计使用的样式
            try:
                styles_used = {}
                for paragraph in doc.Paragraphs:
                    style_name = getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal'
                    styles_used[style_name] = styles_used.get(style_name, 0) + 1
                
                structure['styles_used'] = [
                    {'style': style, 'count': count}
                    for style, count in sorted(styles_used.items(), key=lambda x: x[1], reverse=True)
                ]
                
            except Exception as e:
                logger.warning(f"统计样式使用失败: {str(e)}")
            
            # 分析节信息
            try:
                sections = []
                for i, section in enumerate(doc.Sections):
                    section_info = {
                        'index': i + 1,
                        'page_setup': {
                            'page_width': getattr(section.PageSetup, 'PageWidth', 0),
                            'page_height': getattr(section.PageSetup, 'PageHeight', 0),
                            'top_margin': getattr(section.PageSetup, 'TopMargin', 0),
                            'bottom_margin': getattr(section.PageSetup, 'BottomMargin', 0),
                            'left_margin': getattr(section.PageSetup, 'LeftMargin', 0),
                            'right_margin': getattr(section.PageSetup, 'RightMargin', 0)
                        }
                    }
                    sections.append(section_info)
                
                structure['sections'] = sections
                
            except Exception as e:
                logger.warning(f"分析节信息失败: {str(e)}")
            
            # 🔥 获取完整的统计信息
            try:
                # 基础统计信息
                statistics = {
                    'paragraphs': doc.ComputeStatistics(4),  # 4 = wdStatisticParagraphs
                    'tables': doc.Tables.Count,
                    'images': len([s for s in doc.InlineShapes if hasattr(s, 'Type') and s.Type == 3]), # 3 = wdInlineShapePicture
                    'sections': doc.Sections.Count,
                    'pages': doc.ComputeStatistics(2),  # 2 = wdStatisticPages
                    'words': doc.ComputeStatistics(0),  # 0 = wdStatisticWords
                    'characters': doc.ComputeStatistics(3),  # 3 = wdStatisticCharacters
                    'characters_with_spaces': doc.ComputeStatistics(5),  # 5 = wdStatisticCharactersWithSpaces
                    'lines': doc.ComputeStatistics(1),  # 1 = wdStatisticLines
                }

                # 🔥 获取扩展统计信息
                extended_stats = self._get_extended_statistics(doc)
                statistics.update(extended_stats)

                structure['statistics'] = statistics

            except Exception as e:
                logger.warning(f"获取文档统计信息失败: {str(e)}")
            
            # 提取封面页信息
            try:
                cover_page_info = self._extract_cover_page_info(doc)
                structure['cover_page_info'] = cover_page_info
                
            except Exception as e:
                logger.warning(f"提取封面页信息失败: {str(e)}")
            
            return structure
            
        except Exception as e:
            logger.error(f"分析文档结构失败: {str(e)}")
            raise DocumentProcessorError(f"分析文档结构失败: {str(e)}")
    
    def _get_heading_level(self, style_name: str) -> int:
        """获取标题级别"""
        style_name = style_name.lower()
        
        # 中文标题样式
        if '标题' in style_name:
            for i in range(1, 10):
                if f'标题 {i}' in style_name or f'标题{i}' in style_name:
                    return i
            return 1
        
        # 英文标题样式
        if 'heading' in style_name:
            for i in range(1, 10):
                if f'heading {i}' in style_name or f'heading{i}' in style_name:
                    return i
            return 1
        
        return 0

    def _is_centered_title(self, paragraph: Any, text: str, paragraph_index: int = 0) -> bool:
        """
        判断段落是否为居中的标题

        Args:
            paragraph: Word段落对象
            text: 段落文本
            paragraph_index: 段落索引

        Returns:
            是否为居中标题
        """
        try:
            # 检查文本长度（标题通常较短，但不能太短）
            if len(text) > 100 or len(text) < 2:
                return False

            # 检查是否为居中对齐
            alignment = self._get_paragraph_alignment(paragraph)
            if alignment != 'center':
                return False

            # 检查字体大小（标题通常比正文大）
            font_size = self._get_paragraph_font_size(paragraph)
            if font_size and font_size > 12:  # 大于12号字体
                logger.info(f"检测到大字号居中文本: '{text}' (字号: {font_size})")

            # 1. 检查是否为论文结构关键词
            structure_keywords = [
                # 声明和授权
                '声明', '授权', '原创性声明', '版权使用授权',
                # 摘要和关键词
                '摘要', 'abstract', '关键词', 'keywords',
                # 目录和致谢
                '目录', 'contents', '致谢', '附录',
                # 参考文献
                '参考文献', 'references', '文献',
                # 引言和结论
                '引言', 'introduction', '结论', 'conclusion', '总结',
                # 学位论文相关
                '学位论文', '毕业论文', '学士学位论文', '硕士学位论文', '博士学位论文',
                # 报告相关
                '开题报告', '任务书', '中期报告'
            ]

            text_lower = text.lower()
            for keyword in structure_keywords:
                if keyword in text_lower:
                    logger.info(f"通过关键词识别居中标题: '{text}' (关键词: {keyword})")
                    return True

            # 2. 检查是否为章节编号格式
            for pattern in COMPILED_PATTERNS['chapter']:
                if pattern.match(text):
                    logger.info(f"通过章节格式识别居中标题: '{text}'")
                    return True

            # 3. 特殊情况：如果是居中且字体较大，可能是标题
            if font_size and font_size >= 14 and len(text) <= 30:
                logger.info(f"通过字体大小识别居中标题: '{text}' (字号: {font_size})")
                return True

            # 4. 如果在前几页且居中，可能是封面相关标题
            if paragraph_index <= 50 and len(text) <= 50:  # 前50个段落
                # 检查是否包含论文标题特征
                title_indicators = ['研究', '分析', '探讨', '影响', '应用', '发展', '创新', '技术']
                for indicator in title_indicators:
                    if indicator in text:
                        logger.info(f"通过论文标题特征识别居中标题: '{text}'")
                        return True

            return False

        except Exception as e:
            logger.warning(f"检查居中标题失败: {str(e)}")
            return False

    def _get_paragraph_alignment(self, paragraph: Any) -> str:
        """
        获取段落对齐方式

        Args:
            paragraph: Word段落对象

        Returns:
            对齐方式: 'left', 'center', 'right', 'justify'
        """
        try:
            # Word对齐常量
            # 0 = wdAlignParagraphLeft
            # 1 = wdAlignParagraphCenter
            # 2 = wdAlignParagraphRight
            # 3 = wdAlignParagraphJustify

            # 尝试多种方式获取对齐方式
            alignment = 0  # 默认左对齐

            # 方式1：直接访问ParagraphFormat.Alignment
            try:
                if hasattr(paragraph, 'ParagraphFormat'):
                    para_format = paragraph.ParagraphFormat
                    if hasattr(para_format, 'Alignment'):
                        alignment = para_format.Alignment
            except:
                pass

            # 方式2：通过Range访问
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'ParagraphFormat'):
                        para_format = range_obj.ParagraphFormat
                        if hasattr(para_format, 'Alignment'):
                            alignment = para_format.Alignment
            except:
                pass

            alignment_map = {
                0: 'left',
                1: 'center',
                2: 'right',
                3: 'justify'
            }

            return alignment_map.get(alignment, 'left')

        except Exception as e:
            logger.warning(f"获取段落对齐方式失败: {str(e)}")
            return 'left'

    def _get_paragraph_font_size(self, paragraph: Any) -> float:
        """
        获取段落字体大小

        Args:
            paragraph: Word段落对象

        Returns:
            字体大小（磅）
        """
        try:
            # 尝试多种方式获取字体大小
            font_size = None

            # 方式1：通过Range.Font.Size
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Font'):
                        font = range_obj.Font
                        if hasattr(font, 'Size'):
                            font_size = font.Size
            except:
                pass

            # 方式2：通过第一个字符的格式
            try:
                if not font_size and hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if range_obj.Characters.Count > 0:
                        first_char = range_obj.Characters(1)
                        if hasattr(first_char, 'Font'):
                            font = first_char.Font
                            if hasattr(font, 'Size'):
                                font_size = font.Size
            except:
                pass

            return float(font_size) if font_size else None

        except Exception as e:
            logger.warning(f"获取段落字体大小失败: {str(e)}")
            return None

    def _is_paragraph_bold(self, paragraph: Any) -> bool:
        """
        检查段落是否为粗体

        Args:
            paragraph: Word段落对象

        Returns:
            是否为粗体
        """
        try:
            # 方式1：通过Range.Font.Bold
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Font'):
                        font = range_obj.Font
                        if hasattr(font, 'Bold'):
                            return bool(font.Bold)
            except:
                pass

            # 方式2：通过第一个字符的格式
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if range_obj.Characters.Count > 0:
                        first_char = range_obj.Characters(1)
                        if hasattr(first_char, 'Font'):
                            font = first_char.Font
                            if hasattr(font, 'Bold'):
                                return bool(font.Bold)
            except:
                pass

            return False

        except Exception as e:
            logger.warning(f"检查段落粗体失败: {str(e)}")
            return False

    def _detect_cover_page_structure(self, doc: Any) -> dict:
        """
        检测封面页结构

        Args:
            doc: Word文档对象

        Returns:
            封面页结构信息
        """
        try:
            cover_info = {
                'has_cover': False,
                'title_found': False,
                'author_found': False,
                'advisor_found': False,
                'school_found': False,
                'date_found': False,
                'cover_elements': [],
                'cover_end_paragraph': 30  # 默认封面结束段落
            }

            # 检查前50个段落，寻找封面结束标志
            paragraph_count = min(50, doc.Paragraphs.Count)
            cover_end_found = False

            for i in range(1, paragraph_count + 1):
                try:
                    paragraph = doc.Paragraphs(i)
                    text = paragraph.Range.Text.strip()

                    if not text or len(text) < 2:
                        continue

                    # 检查是否为封面结束标志（如"摘要"、"目录"、正文开始等）
                    if self._is_cover_end_marker(text):
                        cover_info['cover_end_paragraph'] = i - 1
                        cover_end_found = True
                        logger.info(f"检测到封面结束标志: '{text}' 在段落 {i}")
                        break

                    # 只在封面范围内检查封面元素
                    if i <= 30:  # 前30个段落作为封面候选区域
                        # 检查是否包含封面关键信息
                        if self._is_title_text(text):
                            cover_info['title_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'title',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_author_text(text):
                            cover_info['author_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'author',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_advisor_text(text):
                            cover_info['advisor_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'advisor',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_school_text(text):
                            cover_info['school_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'school',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_date_text(text):
                            cover_info['date_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'date',
                                'text': text,
                                'paragraph_index': i
                            })

                except Exception as e:
                    continue

            # 判断是否有封面
            found_elements = sum([
                cover_info['title_found'],
                cover_info['author_found'],
                cover_info['advisor_found'],
                cover_info['school_found'],
                cover_info['date_found']
            ])

            cover_info['has_cover'] = found_elements >= 3  # 至少找到3个关键元素

            if cover_info['has_cover']:
                logger.info(f"检测到封面页，包含 {found_elements} 个关键元素，封面结束于段落 {cover_info['cover_end_paragraph']}")

            return cover_info

        except Exception as e:
            logger.warning(f"检测封面页结构失败: {str(e)}")
            return {'has_cover': False, 'cover_elements': [], 'cover_end_paragraph': 0}

    def _is_title_text(self, text: str) -> bool:
        """检查是否为论文标题"""
        title_indicators = [
            '研究', '分析', '探讨', '影响', '应用', '发展', '创新', '技术',
            '系统', '方法', '设计', '实现', '优化', '策略', '模式', '理论'
        ]
        # 标题通常较长且包含研究相关词汇
        return (len(text) > 8 and len(text) < 100 and
                any(indicator in text for indicator in title_indicators))

    def _is_author_text(self, text: str) -> bool:
        """检查是否为作者信息"""
        author_patterns = [
            r'姓\s*名', r'学生姓名', r'作\s*者', r'学\s*生',
            r'姓名.*[：:]', r'学生.*[：:]'
        ]
        import re
        return any(re.search(pattern, text) for pattern in author_patterns)

    def _is_advisor_text(self, text: str) -> bool:
        """检查是否为指导教师信息"""
        advisor_patterns = [
            r'指导教师', r'导\s*师', r'指导老师', r'指导.*[：:]'
        ]
        import re
        return any(re.search(pattern, text) for pattern in advisor_patterns)

    def _is_school_text(self, text: str) -> bool:
        """检查是否为学校信息"""
        school_keywords = [
            '大学', '学院', '学校', '科技学院', '师范大学', '理工大学'
        ]
        return any(keyword in text for keyword in school_keywords)

    def _is_date_text(self, text: str) -> bool:
        """检查是否为日期信息"""
        import re
        date_patterns = [
            r'\d{4}年\d{1,2}月',  # 2024年5月
            r'二〇\d+年',  # 二〇二四年
            r'\d{4}-\d{1,2}-\d{1,2}',  # 2024-05-20
            r'\d{4}/\d{1,2}/\d{1,2}'   # 2024/05/20
        ]
        return any(re.search(pattern, text) for pattern in date_patterns)

    def _is_cover_end_marker(self, text: str) -> bool:
        """
        检查是否为封面结束标志

        Args:
            text: 段落文本

        Returns:
            是否为封面结束标志
        """
        # 封面结束标志（论文正文开始的标志）
        end_markers = [
            '摘要', 'abstract', '目录', 'contents',
            '学位论文原创性声明', '学位论文版权使用授权书',
            '声明', '授权书', '原创性声明', '版权使用授权',
            '绪论', '引言', 'introduction', '第一章', '第1章',
            '1.', '1 ', 'chapter 1', 'chapter1'
        ]

        text_lower = text.lower().strip()

        # 精确匹配或开头匹配
        for marker in end_markers:
            if text_lower == marker.lower() or text_lower.startswith(marker.lower()):
                return True

        # 检查章节编号格式
        import re
        chapter_patterns = [
            r'^第[一二三四五六七八九十\d]+章',  # 第X章
            r'^\d+\.?\s*[^\d]',  # 数字开头
            r'^[一二三四五六七八九十]+[、．.]',  # 中文数字开头
        ]

        for pattern in chapter_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _get_content_heading_level(self, text: str) -> int:
        """
        根据内容特征判断标题级别

        Args:
            text: 标题文本

        Returns:
            标题级别
        """
        text_lower = text.lower()

        # 一级标题关键词（重要章节）
        level1_keywords = [
            '摘要', 'abstract', '目录', 'contents', '致谢', '附录',
            '参考文献', 'references', '声明', '授权', '第一章', '第二章',
            '第三章', '第四章', '第五章', '第六章', 'chapter'
        ]

        # 二级标题关键词
        level2_keywords = [
            '关键词', 'keywords', '引言', 'introduction', '结论', 'conclusion'
        ]

        for keyword in level1_keywords:
            if keyword in text_lower:
                return 1

        for keyword in level2_keywords:
            if keyword in text_lower:
                return 2

        # 检查章节编号
        import re
        if re.match(r'^第[一二三四五六七八九十\d]+章', text):
            return 1
        elif re.match(r'^\d+\.?\s*[^\d]', text):
            return 2
        elif re.match(r'^[一二三四五六七八九十]+[、．.]', text):
            return 3

        return 1  # 默认为一级标题
    
    def _extract_cover_page_info(self, doc: Any) -> Dict[str, Any]:
        """
        从文档第一页提取封面页信息
        
        Args:
            doc: Word文档对象
            
        Returns:
            Dict[str, Any]: 封面页信息
        """
        try:
            import re
            
            # 初始化封面页信息
            cover_info = {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': ''
            }
            
            # 获取第一页内容
            first_page_text = self._get_first_page_content(doc)
            cover_info['raw_text'] = first_page_text
            
            logger.info(f"第一页文本内容: {first_page_text[:200]}...")
            
            # 使用正则表达式提取信息
            # 🔥 修复：优先使用\r分割，如果没有\r再使用\n分割
            if '\r' in first_page_text:
                lines = first_page_text.split('\r')
            else:
                lines = first_page_text.split('\n')
            lines = [self._clean_text(line) for line in lines if line.strip()]
            
            # 提取学校名称
            for line in lines:
                if any(keyword in line for keyword in ['学院', '大学', '学校', '科技学院', '科技大学']):
                    # 过滤掉包含其他关键词的行
                    if not any(keyword in line for keyword in ['院系', '专业', '导师', '指导教师']):
                        school_value = self._clean_text(line)
                        # 🔥 修复：清理学校信息中的重复前缀
                        school_value = self._clean_school_value(school_value)
                        cover_info['school'] = school_value
                        break
            
            # 提取学位论文类型
            for line in lines:
                if any(keyword in line for keyword in ['学位论文', '毕业论文', '硕士论文', '博士论文', '学士论文']):
                    cover_info['degree_type'] = self._clean_text(line)
                    break
            
            # 🔥 优化：提取论文标题
            # 方式1: 从"设计（论文）题目："后面提取
            title_match = re.search(r'设计\（论文\）题目[：:\s]*([^\r\n]+)', first_page_text)
            if title_match and title_match.group(1).strip():
                cover_info['title'] = self._clean_text(title_match.group(1).strip())
                logger.info(f"从'设计（论文）题目'提取标题: {cover_info['title']}")
            else:
                # 方式2: 查找可能的标题行
                for i, line in enumerate(lines):
                    # 跳过空行、太短的行、或明显不是标题的行
                    if not line or len(line) < 8 or len(line) > 100:
                        continue
                    
                    # 跳过包含特殊标识符的行
                    if any(keyword in line for keyword in [
                        '学院', '大学', '学校', '学位论文', '毕业论文', '毕业设计',
                        '姓名', '学号', '专业', '导师', '指导', '年', '月', '日',
                        '声明', '授权', '签名', '摘要', 'Abstract', '关键词'
                    ]):
                        continue
                    
                    # 跳过英文标题（如果后面紧跟着中文标题）
                    if re.search(r'^[A-Za-z\s]+$', line) and i + 1 < len(lines):
                        # 检查下一行是否是中文标题
                        next_line = lines[i + 1]
                        if next_line and re.search(r'[\u4e00-\u9fff]', next_line) and 8 <= len(next_line) <= 100:
                            continue  # 跳过英文标题，使用中文标题
                    
                    # 检查是否为标题（包含中文字符或有意义的英文内容）
                    if (re.search(r'[\u4e00-\u9fff]', line) or  # 包含中文字符
                        (re.search(r'[A-Za-z]', line) and len(line) > 15)):  # 或较长的英文标题
                        
                        # 进一步过滤：不包含冒号、不全是数字等
                        if not re.search(r'[：:\d]{3,}', line):
                            cover_info['title'] = line
                            logger.info(f"从行分析提取标题: {cover_info['title']} (行{i})")
                            break
            
            # 提取姓名
            name_patterns = [
                r'姓\s*名[：:\s]*([^\s\n]+)',
                r'学生姓名[：:\s]*([^\s\n]+)',
                r'作\s*者[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in name_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['author'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取学号
            student_id_patterns = [
                r'学\s*号[：:\s]*([0-9]+)',
                r'学生学号[：:\s]*([0-9]+)',
                r'学生编号[：:\s]*([0-9]+)',
            ]
            
            for pattern in student_id_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['student_id'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取院系
            department_patterns = [
                r'院\s*系[：:\s]*([^\s\n]+)',
                r'学\s*院[：:\s]*([^\s\n]+)',
                r'系\s*别[：:\s]*([^\s\n]+)',
            ]

            for pattern in department_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    department_value = self._clean_text(match.group(1).strip())
                    # 🔥 修复：清理院系信息中的重复前缀
                    department_value = self._clean_department_value(department_value)
                    cover_info['department'] = department_value
                    break
            
            # 提取专业
            major_patterns = [
                r'专\s*业[：:\s]*([^\s\n]+)',
                r'所学专业[：:\s]*([^\s\n]+)',
                r'专业名称[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in major_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['major'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取指导老师
            advisor_patterns = [
                r'指导[教师]*[：:\s]*([^\s\n]+)',
                r'导\s*师[：:\s]*([^\s\n]+)',
                r'指导老师[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in advisor_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['advisor'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取时间 - 使用改进的中文日期解析器
            date_patterns = [
                # 完整中文日期格式
                r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年\s*([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月\s*([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
                # 中文年月日格式
                r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年\s*([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月',
                # 混合格式：数字年份 + 中文月日
                r'(\d{4})\s*年\s*([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月\s*([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
                # 纯数字格式
                r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
                r'(\d{4})\s*年\s*(\d{1,2})\s*月',
                # 简化中文格式
                r'二[○〇零一二三四五六七八九十][一二三四五六七八九十]*年',
            ]
            
            extracted_date = None
            for pattern in date_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    raw_date = match.group(0).strip()
                    logger.info(f"匹配到原始日期: {raw_date}")
                    
                    # 使用中文日期解析器处理
                    parsed_date = self.date_parser.parse_chinese_date(raw_date)
                    if parsed_date:
                        extracted_date = parsed_date
                        logger.info(f"成功解析日期: {raw_date} -> {parsed_date}")
                        break
                    else:
                        # 如果解析失败，保留原始文本
                        extracted_date = self._clean_text(raw_date)
                        logger.warning(f"日期解析失败，保留原始文本: {raw_date}")
                        break
            
            if extracted_date:
                cover_info['date'] = extracted_date
            else:
                # 尝试查找任何包含年份的行作为备选
                for line in lines:
                    if re.search(r'(19|20)\d{2}', line) and ('年' in line or '月' in line or '日' in line):
                        cover_info['date'] = self._clean_text(line)
                        logger.info(f"备选日期提取: {line}")
                        break
            
            # 记录提取结果
            logger.info(f"封面页信息提取完成: {cover_info}")
            
            return cover_info
            
        except Exception as e:
            logger.error(f"提取封面页信息失败: {str(e)}")
            return {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': '',
                'error': str(e)
            }
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本中的控制字符和多余空白

        Args:
            text: 待清理的文本

        Returns:
            str: 清理后的文本
        """
        if not text:
            return ''

        # 使用预编译的正则表达式移除控制字符
        cleaned = COMPILED_PATTERNS['control_chars'].sub('', text)

        # 将多个连续的空白字符替换为单个空格
        cleaned = re.sub(r'\s+', ' ', cleaned)

        # 去除首尾空白
        cleaned = cleaned.strip()

        return cleaned

    def _clean_department_value(self, value: str) -> str:
        """
        清理院系信息中的重复前缀和多余空格

        Args:
            value: 原始院系信息

        Returns:
            str: 清理后的院系信息
        """
        if not value:
            return ''

        import re

        # 先清理首尾空格和多余空格
        cleaned_value = re.sub(r'\s+', ' ', value.strip())

        # 🔥 精确匹配：处理"院 系 XXX"或"院系XXX"格式
        院系_pattern = r'^院\s*系\s*(.+)$'
        match = re.match(院系_pattern, cleaned_value)
        if match:
            cleaned_value = match.group(1).strip()

        # 🔥 精确匹配：处理"系 别 XXX"或"系别XXX"格式
        系别_pattern = r'^系\s*别\s*(.+)$'
        match = re.match(系别_pattern, cleaned_value)
        if match:
            cleaned_value = match.group(1).strip()

        return cleaned_value

    def _clean_school_value(self, value: str) -> str:
        """
        清理学校信息中的重复前缀和多余空格

        Args:
            value: 原始学校信息

        Returns:
            str: 清理后的学校信息
        """
        if not value:
            return ''

        import re

        # 先清理首尾空格和多余空格
        cleaned_value = re.sub(r'\s+', ' ', value.strip())

        # 🔥 精确匹配：处理"院 系 XXX"或"院系XXX"格式
        院系_pattern = r'^院\s*系\s*(.+)$'
        match = re.match(院系_pattern, cleaned_value)
        if match:
            cleaned_value = match.group(1).strip()

        # 🔥 精确匹配：处理"学 校 XXX"或"学校XXX"格式
        学校_pattern = r'^学\s*校\s*(.+)$'
        match = re.match(学校_pattern, cleaned_value)
        if match:
            cleaned_value = match.group(1).strip()

        return cleaned_value

    def _get_extended_statistics(self, doc) -> Dict[str, Any]:
        """
        获取扩展的文档统计信息

        Args:
            doc: Word文档对象

        Returns:
            dict: 扩展统计信息
        """
        extended_stats = {}

        try:
            # 结构分析统计
            extended_stats.update({
                'heading_count': self._count_headings(doc),
                'footnote_count': doc.Footnotes.Count if hasattr(doc, 'Footnotes') else 0,
                'endnote_count': doc.Endnotes.Count if hasattr(doc, 'Endnotes') else 0,
                'reference_count': self._count_references(doc),
                'hyperlink_count': doc.Hyperlinks.Count if hasattr(doc, 'Hyperlinks') else 0,
                'bookmark_count': doc.Bookmarks.Count if hasattr(doc, 'Bookmarks') else 0,
                'comment_count': doc.Comments.Count if hasattr(doc, 'Comments') else 0,
                'field_count': doc.Fields.Count if hasattr(doc, 'Fields') else 0,
            })

            # 格式规范统计
            format_stats = self._get_format_stats(doc)
            extended_stats.update(format_stats)

            # 质量检查统计
            quality_stats = self._get_quality_stats(doc)
            extended_stats.update(quality_stats)

        except Exception as e:
            logger.warning(f"获取扩展统计信息失败: {str(e)}")

        return extended_stats

    def _count_headings(self, doc) -> int:
        """统计标题数量"""
        heading_count = 0
        try:
            if hasattr(doc, 'Paragraphs'):
                for paragraph in doc.Paragraphs:
                    try:
                        style_name = paragraph.Style.NameLocal if hasattr(paragraph, 'Style') else ''
                        if any(heading in style_name.lower() for heading in ['heading', '标题', 'title']):
                            heading_count += 1
                    except:
                        continue
        except Exception as e:
            logger.warning(f"统计标题数量失败: {str(e)}")
        return heading_count

    def _get_format_stats(self, doc) -> Dict[str, Any]:
        """获取格式统计信息"""
        format_stats = {
            'font_count': 0,
            'style_count': 0,
            'fonts_used': [],
            'styles_used': [],
            'page_orientation': 'unknown',
            'page_size': 'unknown',
            'margin_info': {},
            'line_spacing_info': {},
        }

        try:
            fonts_used = set()
            styles_used = {}

            if hasattr(doc, 'Paragraphs'):
                for paragraph in doc.Paragraphs:
                    try:
                        # 统计样式
                        if hasattr(paragraph, 'Style'):
                            style_name = paragraph.Style.NameLocal
                            styles_used[style_name] = styles_used.get(style_name, 0) + 1

                        # 统计字体
                        if hasattr(paragraph, 'Range') and hasattr(paragraph.Range, 'Font'):
                            font_name = paragraph.Range.Font.Name
                            if font_name:
                                fonts_used.add(font_name)
                    except:
                        continue

            format_stats['font_count'] = len(fonts_used)
            format_stats['style_count'] = len(styles_used)
            format_stats['fonts_used'] = list(fonts_used)[:10]
            format_stats['styles_used'] = [{'name': k, 'count': v} for k, v in sorted(styles_used.items(), key=lambda x: x[1], reverse=True)[:10]]

            # 页面设置
            if hasattr(doc, 'Sections') and doc.Sections.Count > 0:
                try:
                    page_setup = doc.Sections(1).PageSetup
                    format_stats['page_orientation'] = 'portrait' if page_setup.Orientation == 0 else 'landscape'
                    format_stats['page_size'] = f"{page_setup.PageWidth:.0f}x{page_setup.PageHeight:.0f}"
                    format_stats['margin_info'] = {
                        'top': page_setup.TopMargin,
                        'bottom': page_setup.BottomMargin,
                        'left': page_setup.LeftMargin,
                        'right': page_setup.RightMargin,
                    }
                except:
                    pass

        except Exception as e:
            logger.warning(f"获取格式统计失败: {str(e)}")

        return format_stats

    def _get_quality_stats(self, doc) -> Dict[str, int]:
        """获取质量统计信息"""
        quality_stats = {
            'spelling_errors': 0,
            'grammar_errors': 0,
            'revision_count': 0,
            'version_count': 0,
            'track_changes_count': 0,
            'formula_count': 0,
            'equation_count': 0,
            'textbox_count': 0,
            'chart_count': 0,
            'drawing_count': 0,
        }

        try:
            # 拼写和语法错误
            if hasattr(doc, 'SpellingErrors'):
                quality_stats['spelling_errors'] = doc.SpellingErrors.Count
            if hasattr(doc, 'GrammarErrors'):
                quality_stats['grammar_errors'] = doc.GrammarErrors.Count

            # 修订统计
            if hasattr(doc, 'Revisions'):
                quality_stats['revision_count'] = doc.Revisions.Count
            if hasattr(doc, 'Versions'):
                quality_stats['version_count'] = doc.Versions.Count

            # 特殊内容统计
            if hasattr(doc, 'InlineShapes'):
                for shape in doc.InlineShapes:
                    try:
                        shape_type = shape.Type
                        if shape_type == 8:  # 公式
                            quality_stats['formula_count'] += 1
                        elif shape_type == 12:  # 图表
                            quality_stats['chart_count'] += 1
                        elif shape_type == 13:  # 方程式
                            quality_stats['equation_count'] += 1
                    except:
                        continue

            if hasattr(doc, 'Shapes'):
                for shape in doc.Shapes:
                    try:
                        shape_type = shape.Type
                        if shape_type == 17:  # 文本框
                            quality_stats['textbox_count'] += 1
                        elif shape_type in [1, 2, 3, 4, 5]:  # 绘图对象
                            quality_stats['drawing_count'] += 1
                    except:
                        continue

        except Exception as e:
            logger.warning(f"获取质量统计失败: {str(e)}")

        return quality_stats

    def _get_first_page_content(self, doc: Any) -> str:
        """
        获取文档第一页的文本内容
        
        Args:
            doc: Word文档对象
            
        Returns:
            str: 第一页的文本内容
        """
        try:
            # 方法1：通过页面范围获取第一页内容
            try:
                # 获取第一页的范围
                page_range = doc.Range(0, doc.ComputeStatistics(0))  # 从文档开始到第一页结束
                
                # 如果文档有多页，计算第一页的结束位置
                if doc.ComputeStatistics(2) > 1:  # 总页数大于1
                    # 通过段落位置估算第一页结束位置
                    total_chars = doc.Range().Characters.Count
                    estimated_first_page_end = min(2000, total_chars // 2)  # 估算第一页大约的字符数
                    
                    page_range = doc.Range(0, estimated_first_page_end)
                
                first_page_text = page_range.Text
                
                if first_page_text and len(first_page_text.strip()) > 50:
                    return first_page_text
                    
            except Exception as e:
                logger.warning(f"方法1获取第一页内容失败: {str(e)}")
            
            # 方法2：通过前几个段落获取第一页内容
            try:
                first_page_paragraphs = []
                char_count = 0
                max_chars = 3000  # 限制字符数以确保是第一页
                
                for paragraph in doc.Paragraphs:
                    para_text = paragraph.Range.Text
                    
                    # 检查是否超过预期的第一页字符数
                    if char_count + len(para_text) > max_chars:
                        break
                    
                    first_page_paragraphs.append(para_text)
                    char_count += len(para_text)
                    
                    # 如果累计段落数过多，可能已经超过第一页
                    if len(first_page_paragraphs) > 50:
                        break
                
                first_page_text = '\n'.join(first_page_paragraphs)
                
                if first_page_text and len(first_page_text.strip()) > 50:
                    return first_page_text
                    
            except Exception as e:
                logger.warning(f"方法2获取第一页内容失败: {str(e)}")
            
            # 方法3：获取文档前半部分内容作为备用
            try:
                full_text = doc.Range().Text
                if full_text:
                    # 取前2000个字符作为第一页内容
                    first_page_text = full_text[:2000]
                    return first_page_text
                    
            except Exception as e:
                logger.warning(f"方法3获取第一页内容失败: {str(e)}")
            
            # 如果所有方法都失败，返回空字符串
            logger.warning("所有方法都无法获取第一页内容")
            return ""
            
        except Exception as e:
            logger.error(f"获取第一页内容失败: {str(e)}")
            return ""
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        total_operations = self.processing_stats['successful_operations'] + self.processing_stats['failed_operations']
        success_rate = (self.processing_stats['successful_operations'] / total_operations * 100) if total_operations > 0 else 0
        avg_time = (self.processing_stats['total_processing_time'] / total_operations) if total_operations > 0 else 0
        
        return {
            'documents_processed': self.processing_stats['documents_processed'],
            'successful_operations': self.processing_stats['successful_operations'],
            'failed_operations': self.processing_stats['failed_operations'],
            'success_rate': round(success_rate, 2),
            'total_processing_time': round(self.processing_stats['total_processing_time'], 2),
            'avg_processing_time': round(avg_time, 2),
            'using_pool': self.use_pool
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.processing_stats = {
            'documents_processed': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_processing_time': 0.0
        }

    def clear_cache(self):
        """清理缓存"""
        self._file_info_cache.clear()
        self._structure_rules_cache = None
        logger.info("文档处理器缓存已清理")

    def _count_words_in_text(self, text: str) -> int:
        """
        计算文本中的字数

        Args:
            text: 要统计的文本

        Returns:
            字数统计结果
        """
        if not text:
            return 0

        import re

        # 去除HTML标签和多余空白
        clean_text = re.sub(r'<[^>]*>', '', text)
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()

        # 分别计算中文字符和英文单词
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', clean_text))
        english_words = len(re.findall(r'[a-zA-Z]+', clean_text))

        return chinese_chars + english_words

    def _calculate_structure_word_count(self, page_data: dict, structure_name: str, page_num: int, start_paragraph_index: int) -> int:
        """
        计算整个结构的字数统计

        Args:
            page_data: 页面数据
            structure_name: 结构名称
            page_num: 页码
            start_paragraph_index: 起始段落索引

        Returns:
            int: word_count
        """
        try:
            paragraphs = page_data.get('paragraphs', [])

            # 根据结构类型确定统计范围
            if structure_name in ['封面', '任务书', '开题报告', '诚信声明', '版权声明']:
                # 对于这些结构，统计整个页面的所有文字（除了表格）
                word_count, _ = self._count_words_in_page_structure(paragraphs, structure_name)
                return word_count

            elif structure_name in ['中文摘要', '英文摘要', '中文关键词', '英文关键词']:
                # 对于摘要和关键词，从标识段落开始，到下一个主要结构或页面结束
                word_count, _ = self._count_words_in_section_structure(paragraphs, start_paragraph_index, structure_name)
                return word_count

            elif structure_name == '目录':
                # 目录统计所有TOC样式的段落
                word_count, _ = self._count_words_in_toc_structure(paragraphs)
                return word_count

            elif structure_name == '正文':
                # 正文统计从标识段落开始的大量内容
                word_count, _ = self._count_words_in_main_content(paragraphs, start_paragraph_index)
                return word_count

            elif structure_name == '参考文献':
                # 参考文献只统计字数，详细统计在调用处处理
                result = self._count_words_in_references(paragraphs, start_paragraph_index)
                if len(result) == 4:
                    # 新格式：(word_count, total_reference_count, chinese_count, foreign_count)
                    word_count, total_reference_count, chinese_count, foreign_count = result
                    # 存储详细信息到临时变量，稍后在结构数据中使用
                    self._temp_reference_details = {
                        'chinese_count': chinese_count,
                        'foreign_count': foreign_count
                    }
                    return word_count
                else:
                    # 兼容旧格式
                    word_count, _ = result
                    return word_count

            else:
                # 其他结构，使用原有逻辑（单段落）
                if start_paragraph_index < len(paragraphs):
                    text = paragraphs[start_paragraph_index].get('text', '')
                    word_count = self._count_words_in_text(text)
                    return word_count

            return 0

        except Exception as e:
            logger.warning(f"计算结构字数失败 {structure_name}: {str(e)}")
            return 0

    def _count_words_in_page_structure(self, paragraphs: list, structure_name: str) -> tuple:
        """统计页面结构的字数（如封面、任务书等）"""
        total_words = 0
        reference_count = 0

        logger.info(f"🔥 调试：开始统计页面结构 {structure_name}，段落数: {len(paragraphs)}")

        for i, paragraph in enumerate(paragraphs):
            # 跳过表格内容（表格通常不算在正文字数中）
            if paragraph.get('is_in_table', False):
                logger.info(f"🔥 调试：跳过表格内容，段落 {i}")
                continue

            text = paragraph.get('text', '').strip()
            if text:
                word_count = self._count_words_in_text(text)
                total_words += word_count
                logger.info(f"🔥 调试：段落 {i} 字数: {word_count}，内容: {text[:50]}...")
            else:
                logger.info(f"🔥 调试：段落 {i} 为空")

        logger.info(f"🔥 调试：页面结构 {structure_name} 统计字数: {total_words}")
        return total_words, reference_count

    def _count_words_in_section_structure(self, paragraphs: list, start_index: int, structure_name: str) -> tuple:
        """统计章节结构的字数（如摘要、关键词等）"""
        total_words = 0
        reference_count = 0

        # 从起始段落开始统计，直到遇到下一个主要结构标识或页面结束
        for i in range(start_index, len(paragraphs)):
            paragraph = paragraphs[i]

            # 跳过表格内容
            if paragraph.get('is_in_table', False):
                continue

            text = paragraph.get('text', '').strip()
            if not text:
                continue

            # 检查是否遇到了下一个主要结构的开始
            if i > start_index and self._is_major_structure_start(text):
                break

            total_words += self._count_words_in_text(text)

        logger.info(f"章节结构 {structure_name} 统计字数: {total_words}")
        return total_words, reference_count

    def _count_words_in_toc_structure(self, paragraphs: list) -> tuple:
        """统计目录结构的字数"""
        total_words = 0
        reference_count = 0

        for paragraph in paragraphs:
            style = paragraph.get('style', 'Normal')
            # 统计所有TOC样式的段落
            if 'TOC' in style.upper() or 'toc' in style.lower() or '目录' in style:
                text = paragraph.get('text', '').strip()
                if text:
                    total_words += self._count_words_in_text(text)

        logger.info(f"目录结构统计字数: {total_words}")
        return total_words, reference_count

    def _count_words_in_main_content(self, paragraphs: list, start_index: int) -> tuple:
        """统计正文结构的字数"""
        total_words = 0
        reference_count = 0

        # 正文通常包含大量内容，从标识段落开始统计相当长的范围
        end_index = min(start_index + 50, len(paragraphs))  # 统计后续50个段落或到页面结束

        for i in range(start_index, end_index):
            paragraph = paragraphs[i]

            # 跳过表格内容
            if paragraph.get('is_in_table', False):
                continue

            text = paragraph.get('text', '').strip()
            if text:
                total_words += self._count_words_in_text(text)

        logger.info(f"正文结构统计字数: {total_words}")
        return total_words, reference_count

    def _count_words_in_references(self, paragraphs: list, start_index: int) -> tuple:
        """统计参考文献结构的字数和条数"""
        total_words = 0

        # 收集所有参考文献文本
        all_reference_text = ""

        # 从标识段落开始，统计所有参考文献条目
        for i in range(start_index, len(paragraphs)):
            paragraph = paragraphs[i]

            # 🔥 修复：对于参考文献，不跳过表格内容，因为参考文献可能在表格中
            text = paragraph.get('text', '').strip()
            if not text:
                continue

            total_words += self._count_words_in_text(text)
            all_reference_text += text + "\n"

        # 统计参考文献条数（按语言分类）
        chinese_count, foreign_count = self._count_references_by_language(all_reference_text)
        total_reference_count = chinese_count + foreign_count

        logger.info(f"参考文献结构统计字数: {total_words}, 参考文献条数: {total_reference_count} (中文{chinese_count}条, 外文{foreign_count}条)")

        # 返回总字数、总条数、中文条数、外文条数
        return total_words, total_reference_count, chinese_count, foreign_count

    def _extract_full_reference_content(self, page_data: list, start_index: int) -> str:
        """
        提取完整的参考文献内容

        Args:
            page_data: 页面数据列表
            start_index: 参考文献标识段落的索引

        Returns:
            完整的参考文献文本内容
        """
        full_content = ""

        # 从标识段落开始，提取所有参考文献内容
        for i in range(start_index, len(page_data)):
            paragraph = page_data[i]

            text = paragraph.get('text', '').strip()
            if not text:
                continue

            # 🔥 修复：对于参考文献，不跳过表格内容，因为参考文献可能在表格中
            # 检查是否遇到了下一个主要结构的开始
            if i > start_index and self._is_major_structure_start(text):
                break

            full_content += text + "\n"

        return full_content.strip()

    def _enhance_reference_structures_with_cross_page_content(self, structures: list, pages_content: dict) -> None:
        """
        增强参考文献结构，使用跨页面内容提取

        Args:
            structures: 结构列表
            pages_content: 所有页面内容
        """
        logger.info(f"🔍 开始增强参考文献结构，总结构数: {len(structures)}")
        for structure in structures:
            if structure.get('name') == '参考文献':
                logger.info(f"🔍 找到参考文献结构，当前内容长度: {len(structure.get('content', {}).get('text', ''))}")

                # 🔥 修复：查找实际包含参考文献条目的页面
                actual_ref_page = self._find_actual_reference_page(pages_content)
                logger.info(f"正在为参考文献结构进行跨页面内容提取，实际参考文献页面: {actual_ref_page}")

                # 跨页面提取参考文献内容
                cross_page_content = self._extract_reference_content_across_pages(pages_content, actual_ref_page)

                if cross_page_content:
                    # 更新结构内容
                    structure['content']['text'] = cross_page_content
                    structure['content']['needs_cross_page_extraction'] = False
                    structure['page'] = actual_ref_page  # 更新页面号为实际页面

                    # 重新计算参考文献语言统计
                    chinese_count, foreign_count = self._count_references_by_language(cross_page_content)

                    # 🔥 新增：更新统一的count字段
                    if chinese_count > 0 and foreign_count > 0:
                        count_display = f"中文{chinese_count}条;外文{foreign_count}条"
                    elif chinese_count > 0:
                        count_display = f"中文{chinese_count}条"
                    elif foreign_count > 0:
                        count_display = f"外文{foreign_count}条"
                    else:
                        count_display = f"{chinese_count + foreign_count}条"

                    # 更新结构统计信息
                    structure['count'] = count_display
                    structure['reference_chinese_count'] = chinese_count
                    structure['reference_foreign_count'] = foreign_count

                    logger.info(f"参考文献跨页面提取完成: {count_display}")
                else:
                    logger.warning(f"参考文献跨页面提取未找到更多内容")

    def _extract_reference_content_across_pages(self, pages_content: dict, reference_page: int) -> str:
        """
        跨页面提取参考文献内容

        Args:
            pages_content: 所有页面内容
            reference_page: 参考文献开始的页面

        Returns:
            完整的参考文献文本内容
        """
        full_content = ""

        # 🔥 修复：优先从最佳参考文献页面提取，然后从其他页面补充
        # 使用字典来按编号去重，确保每个编号只有一个参考文献条目
        extracted_refs_by_number = {}

        # 🔥 新增：优先处理最佳参考文献页面
        pages_to_process = [reference_page] + [p for p in sorted(pages_content.keys()) if p != reference_page]

        for page_num in pages_to_process:
            page_data = pages_content[page_num]

            # 处理新的数据结构：{page_number: {'paragraphs': [...], 'tables': [...]}}
            all_paragraphs = []
            if isinstance(page_data, dict):
                all_paragraphs.extend(page_data.get('paragraphs', []))
                all_paragraphs.extend(page_data.get('tables', []))
            elif isinstance(page_data, list):
                all_paragraphs = page_data
            else:
                continue

            for paragraph in all_paragraphs:
                if isinstance(paragraph, dict):
                    text = paragraph.get('text', '').strip()
                else:
                    text = str(paragraph).strip()

                if not text:
                    continue

                # 🔥 修复：清理文本中的控制字符
                import re
                # 清理控制字符和多余的空白字符
                cleaned_text = re.sub(r'[\r\x07\x0c]+', ' ', text)
                cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

                # 🔥 修复：使用更准确的参考文献格式检查
                # 检查是否包含标准参考文献格式：[数字]作者.标题[文献类型].
                # 扩展文献类型字符集，包含更多可能的类型
                if re.search(r'\[\d+\][^[\]]*\[[A-Za-z]\]', cleaned_text):
                    # 🔥 新增：提取参考文献编号
                    number_match = re.search(r'\[(\d+)\]', cleaned_text)
                    if number_match:
                        ref_number = int(number_match.group(1))
                        # 🔥 新增：按编号去重，优先保留来自最佳页面的条目
                        if ref_number not in extracted_refs_by_number:
                            # 第一次遇到这个编号，直接添加
                            extracted_refs_by_number[ref_number] = (cleaned_text, page_num)
                            logger.info(f"提取参考文献条目: [{ref_number}] {cleaned_text[:50]}... (来自第{page_num}页)")
                        else:
                            existing_text, existing_page = extracted_refs_by_number[ref_number]
                            if page_num == reference_page:
                                # 如果当前是最佳参考文献页面，优先使用其条目
                                extracted_refs_by_number[ref_number] = (cleaned_text, page_num)
                                logger.info(f"更新参考文献条目: [{ref_number}] {cleaned_text[:50]}... (来自最佳页面第{page_num}页)")
                            elif existing_page == reference_page:
                                # 如果现有条目来自最佳页面，不要覆盖
                                logger.info(f"保留参考文献条目: [{ref_number}] {existing_text[:50]}... (保持最佳页面第{existing_page}页的版本)")
                            elif self._is_better_reference(cleaned_text, existing_text):
                                # 否则使用更好的条目
                                extracted_refs_by_number[ref_number] = (cleaned_text, page_num)
                                logger.info(f"更新参考文献条目: [{ref_number}] {cleaned_text[:50]}... (更好的版本，来自第{page_num}页)")
                # 🔥 修复：更严格的参考文献格式检查，避免误匹配正文内容
                # 只匹配以 [数字] 开头，且包含作者、标题等典型参考文献元素的内容
                elif re.search(r'^\[\d+\][^[\]]*[.。][^[\]]*[,，][^[\]]*[.。]', cleaned_text):
                    # 🔥 新增：提取参考文献编号
                    number_match = re.search(r'\[(\d+)\]', cleaned_text)
                    if number_match:
                        ref_number = int(number_match.group(1))
                        # 🔥 新增：按编号去重，优先保留来自最佳页面的条目
                        if ref_number not in extracted_refs_by_number:
                            # 第一次遇到这个编号，直接添加
                            extracted_refs_by_number[ref_number] = (cleaned_text, page_num)
                            logger.info(f"提取参考文献条目: [{ref_number}] {cleaned_text[:50]}... (来自第{page_num}页)")
                        else:
                            existing_text, existing_page = extracted_refs_by_number[ref_number]
                            if page_num == reference_page:
                                # 如果当前是最佳参考文献页面，优先使用其条目
                                extracted_refs_by_number[ref_number] = (cleaned_text, page_num)
                                logger.info(f"更新参考文献条目: [{ref_number}] {cleaned_text[:50]}... (来自最佳页面第{page_num}页)")
                            elif existing_page == reference_page:
                                # 如果现有条目来自最佳页面，不要覆盖
                                logger.info(f"保留参考文献条目: [{ref_number}] {existing_text[:50]}... (保持最佳页面第{existing_page}页的版本)")
                            elif self._is_better_reference(cleaned_text, existing_text):
                                # 否则使用更好的条目
                                extracted_refs_by_number[ref_number] = (cleaned_text, page_num)
                                logger.info(f"更新参考文献条目: [{ref_number}] {cleaned_text[:50]}... (更好的版本，来自第{page_num}页)")

        # 🔥 新增：按编号顺序输出参考文献
        for ref_number in sorted(extracted_refs_by_number.keys()):
            text, page_num = extracted_refs_by_number[ref_number]
            full_content += text + "\n"

        return full_content.strip()

    def _is_better_reference(self, new_ref: str, existing_ref: str) -> bool:
        """
        判断新的参考文献条目是否比现有的更好

        Args:
            new_ref: 新的参考文献条目
            existing_ref: 现有的参考文献条目

        Returns:
            True if 新条目更好, False otherwise
        """
        import re

        # 1. 优先选择更长的条目（更完整）
        if len(new_ref) > len(existing_ref) * 1.2:  # 新条目明显更长
            return True
        elif len(existing_ref) > len(new_ref) * 1.2:  # 现有条目明显更长
            return False

        # 2. 如果长度相近，优先选择包含中文作者的条目
        new_has_chinese_author = bool(re.search(r'\[\d+\][\u4e00-\u9fff]+', new_ref))
        existing_has_chinese_author = bool(re.search(r'\[\d+\][\u4e00-\u9fff]+', existing_ref))

        if new_has_chinese_author and not existing_has_chinese_author:
            return True
        elif existing_has_chinese_author and not new_has_chinese_author:
            return False

        # 3. 如果都有中文作者或都没有，优先选择更完整的格式
        # 检查是否包含完整的出版信息
        new_has_complete_info = bool(re.search(r'\[\d+\][^[\]]*\[[A-Za-z]\][^[\]]*\d{4}', new_ref))
        existing_has_complete_info = bool(re.search(r'\[\d+\][^[\]]*\[[A-Za-z]\][^[\]]*\d{4}', existing_ref))

        if new_has_complete_info and not existing_has_complete_info:
            return True
        elif existing_has_complete_info and not new_has_complete_info:
            return False

        # 4. 默认保留现有条目
        return False

    def _find_actual_reference_page(self, pages_content: dict) -> int:
        """
        查找实际包含参考文献条目的页面

        Args:
            pages_content: 所有页面内容

        Returns:
            包含最多参考文献条目的页面号
        """
        page_ref_counts = {}

        for page_num, page_data in pages_content.items():
            ref_count = 0

            # 处理新的数据结构：{page_number: {'paragraphs': [...], 'tables': [...]}}
            all_paragraphs = []
            if isinstance(page_data, dict):
                all_paragraphs.extend(page_data.get('paragraphs', []))
                all_paragraphs.extend(page_data.get('tables', []))
            elif isinstance(page_data, list):
                all_paragraphs = page_data
            else:
                continue

            for paragraph in all_paragraphs:
                if isinstance(paragraph, dict):
                    text = paragraph.get('text', '').strip()
                else:
                    text = str(paragraph).strip()

                if not text:
                    continue

                # 🔥 修复：清理文本中的控制字符
                import re
                # 清理控制字符和多余的空白字符
                cleaned_text = re.sub(r'[\r\x07\x0c]+', ' ', text)
                cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

                # 🔥 修复：更准确的参考文献格式检查
                # 检查是否包含标准参考文献格式：[数字]作者.标题[文献类型].
                # 扩展文献类型字符集，包含更多可能的类型
                if re.search(r'\[\d+\][^[\]]*\[[A-Za-z]\]', cleaned_text):
                    ref_count += 1
                    logger.info(f"在第{page_num}页找到参考文献条目: {cleaned_text[:50]}...")
                # 🔥 修复：更严格的参考文献格式检查，避免误匹配正文内容
                # 只匹配以 [数字] 开头，且包含作者、标题等典型参考文献元素的内容
                elif re.search(r'^\[\d+\][^[\]]*[.。][^[\]]*[,，][^[\]]*[.。]', cleaned_text):
                    ref_count += 1
                    logger.info(f"在第{page_num}页找到参考文献条目: {cleaned_text[:50]}...")

            # 🔥 修复：移除页面级别的重复检测逻辑，避免重复计数
            # 只在段落级别检测参考文献条目，确保不重复

            if ref_count > 0:
                page_ref_counts[page_num] = ref_count
                logger.info(f"第{page_num}页包含{ref_count}条参考文献")

        # 🔥 修复：选择包含最多参考文献且中文参考文献比例较高的页面
        if page_ref_counts:
            # 计算每个页面的中文参考文献比例
            page_scores = {}
            for page_num, ref_count in page_ref_counts.items():
                # 获取该页面的参考文献内容
                page_data = pages_content.get(page_num, {})
                all_paragraphs = []
                if isinstance(page_data, dict):
                    all_paragraphs.extend(page_data.get('paragraphs', []))
                    all_paragraphs.extend(page_data.get('tables', []))
                elif isinstance(page_data, list):
                    all_paragraphs = page_data

                chinese_refs = 0
                total_refs = 0

                for paragraph in all_paragraphs:
                    if isinstance(paragraph, dict):
                        text = paragraph.get('text', '').strip()
                    else:
                        text = str(paragraph).strip()

                    if not text:
                        continue

                    # 清理文本
                    cleaned_text = re.sub(r'[\r\x07\x0c]+', ' ', text)
                    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

                    # 检查是否是参考文献条目
                    if re.search(r'\[\d+\][^[\]]*\[[A-Za-z]\]', cleaned_text) or re.search(r'^\[\d+\][^[\]]*[.。][^[\]]*[,，][^[\]]*[.。]', cleaned_text):
                        total_refs += 1
                        if self._is_chinese_reference(cleaned_text):
                            chinese_refs += 1

                # 计算得分：参考文献数量 + 中文比例加权
                chinese_ratio = chinese_refs / max(total_refs, 1)
                score = ref_count + (chinese_ratio * 5)  # 中文比例高的页面得到额外加分
                page_scores[page_num] = score
                logger.info(f"第{page_num}页: {ref_count}条参考文献, 中文{chinese_refs}条, 比例{chinese_ratio:.2f}, 得分{score:.2f}")

            # 选择得分最高的页面
            best_page = max(page_scores.keys(), key=lambda x: page_scores[x])
            logger.info(f"选择第{best_page}页作为参考文献页面，得分{page_scores[best_page]:.2f}")
            return best_page

        logger.warning("未找到包含参考文献的页面，默认返回第1页")
        return 1  # 默认返回第1页

    def _is_major_structure_start(self, text: str) -> bool:
        """
        检查文本是否是主要结构的开始

        Args:
            text: 要检查的文本

        Returns:
            True if 是主要结构的开始, False otherwise
        """
        # 🔥 修复：更精确的主要结构识别，避免过早停止参考文献内容提取
        # 只识别明确的结构标题，而不是内容中的关键词
        major_structure_patterns = [
            # 致谢相关
            r'^致\s*谢$', r'^谢\s*辞$', r'^致\s*谢\s*词$',
            # 附录相关
            r'^附\s*录', r'^附\s*件',
            # 结论相关（需要是独立的标题）
            r'^结\s*论$', r'^结\s*语$', r'^总\s*结$', r'^结\s*　\s*　\s*论$',
            # 摘要相关
            r'^摘\s*要$', r'^摘\s*　\s*　\s*要$', r'^abstract$',
            # 目录相关
            r'^目\s*录$', r'^目\s*　\s*　\s*录$', r'^目\s*次$',
            # 引言相关
            r'^引\s*言$', r'^绪\s*论$', r'^前\s*言$',
            # 章节标题
            r'^第[一二三四五六七八九十\d]+章', r'^第\s*[1-9]\d*\s*章',
            # 一级标题格式
            r'^[1-9]\d*\.\s*[^.]*$', r'^[一二三四五六七八九十]\s*、'
        ]

        import re
        text_clean = text.strip()

        for pattern in major_structure_patterns:
            if re.match(pattern, text_clean, re.IGNORECASE):
                return True

        return False

    def _count_references_in_text(self, text: str) -> int:
        """
        计算参考文献条数（仅返回总数，用于向后兼容）

        Args:
            text: 参考文献文本

        Returns:
            参考文献总条数
        """
        chinese_count, foreign_count = self._count_references_by_language(text)
        return chinese_count + foreign_count

    def _count_references_by_language(self, text: str) -> tuple[int, int]:
        """
        按语言分类计算参考文献条数

        Args:
            text: 参考文献文本

        Returns:
            (中文参考文献条数, 外文参考文献条数)
        """
        if not text:
            return 0, 0

        import re

        # 按行分割，过滤空行
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # 查找参考文献格式的行
        reference_patterns = [
            r'^\s*\[\d+\]',  # [1] 格式
            r'^\s*\d+\.\s*',  # 1. 格式
            r'^\s*\(\d+\)',   # (1) 格式
        ]

        chinese_count = 0
        foreign_count = 0
        processed_numbers = set()  # 避免重复计算同一条参考文献

        for line in lines:
            # 检查是否是参考文献开始行
            ref_number = None
            for pattern in reference_patterns:
                match = re.match(pattern, line)
                if match:
                    # 提取参考文献编号
                    number_match = re.search(r'\d+', match.group())
                    if number_match:
                        ref_number = int(number_match.group())
                    break

            if ref_number is not None and ref_number not in processed_numbers:
                processed_numbers.add(ref_number)

                # 判断是中文还是外文参考文献
                if self._is_chinese_reference(line):
                    chinese_count += 1
                else:
                    foreign_count += 1

        # 🔥 修复：如果内容不完整，使用合理的估算
        if chinese_count == 0 and foreign_count == 0 and lines:
            # 检查是否有参考文献编号
            total_refs_found = len(re.findall(r'\[\d+\]', text))
            if total_refs_found > 0:
                # 基于找到的参考文献数量进行估算
                # 分析现有内容的语言特征
                chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
                english_chars = len(re.findall(r'[a-zA-Z]', text))

                if chinese_chars > english_chars:
                    # 中文为主
                    chinese_count = max(1, int(total_refs_found * 0.8))
                    foreign_count = total_refs_found - chinese_count
                else:
                    # 外文为主
                    foreign_count = max(1, int(total_refs_found * 0.8))
                    chinese_count = total_refs_found - foreign_count

                logger.info(f"参考文献内容不完整，基于{total_refs_found}条进行估算")
            else:
                # 简单估算：假设大部分是中文参考文献
                total_estimated = max(1, len(lines) // 2)
                chinese_count = int(total_estimated * 0.8)  # 假设80%是中文
                foreign_count = total_estimated - chinese_count

        logger.info(f"参考文献统计: 中文{chinese_count}条, 外文{foreign_count}条")
        return chinese_count, foreign_count

    def _is_chinese_reference(self, text: str) -> bool:
        """
        判断参考文献是否为中文

        Args:
            text: 参考文献文本行

        Returns:
            True if 中文参考文献, False if 外文参考文献
        """
        import re

        # 统计中文字符数量
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        # 统计英文字符数量（不包括数字和标点）
        english_chars = re.findall(r'[a-zA-Z]', text)

        # 如果中文字符数量超过英文字符数量，认为是中文参考文献
        return len(chinese_chars) > len(english_chars)

    def _extract_structure_content_range(self, doc: Any, structure: dict, pages_content: dict) -> str:
        """
        提取结构的完整内容范围

        Args:
            doc: Word文档对象
            structure: 结构信息
            pages_content: 页面内容

        Returns:
            结构的完整文本内容
        """
        try:
            page_num = structure.get('page', 1)
            paragraph_index = structure.get('content', {}).get('paragraph_index', 0)

            # 获取当前页面的内容
            page_data = pages_content.get(page_num, {})
            paragraphs = page_data.get('paragraphs', [])

            if paragraph_index < len(paragraphs):
                # 从当前段落开始，尝试获取更多内容
                content_parts = []

                # 添加当前段落
                current_paragraph = paragraphs[paragraph_index]
                content_parts.append(current_paragraph.get('text', ''))

                # 对于某些结构，尝试获取后续相关段落
                structure_name = structure.get('name', '')
                if structure_name in ['中文摘要', '英文摘要', '致谢', '参考文献']:
                    # 获取后续段落直到遇到下一个标题
                    for i in range(paragraph_index + 1, len(paragraphs)):
                        next_paragraph = paragraphs[i]
                        next_text = next_paragraph.get('text', '').strip()

                        # 如果遇到明显的标题格式，停止
                        if (next_paragraph.get('is_bold', False) and
                            len(next_text) < 50 and
                            any(keyword in next_text for keyword in ['第', '章', '节', '摘要', '关键词', '目录', '参考文献', '致谢', '附录'])):
                            break

                        content_parts.append(next_text)

                        # 限制最大段落数，避免获取过多内容
                        if len(content_parts) > 20:
                            break

                return '\n'.join(content_parts)

            return structure.get('content', {}).get('text', '')

        except Exception as e:
            logger.warning(f"提取结构内容范围失败: {str(e)}")
            return structure.get('content', {}).get('text', '')

    def _detect_document_structure_by_rules(self, doc: Any) -> dict:
        """
        基于规则文件检测文档结构

        Args:
            doc: Word文档对象

        Returns:
            文档结构信息
        """
        try:
            # 加载规则文件
            rules = self._load_structure_rules()
            if not rules:
                logger.warning("无法加载结构规则，使用默认检测方式")
                return self._fallback_structure_detection(doc)

            # 按页面分析文档内容
            pages_content = self._extract_pages_content(doc)

            # 按页面顺序检测所有结构（标准和非标准）
            detected_structures = self._detect_structures_by_page_order(pages_content, rules)

            # 🔥 优化：增强document_structures，添加level等字段，移除重复的outline
            enhanced_structures = self._enhance_document_structures(detected_structures)

            return {
                'document_structures': enhanced_structures,
                # 🔥 移除outline，统一使用document_structures
                'structure_analysis_method': 'rule_based'
            }

        except Exception as e:
            logger.error(f"基于规则的结构检测失败: {str(e)}")
            return self._fallback_structure_detection(doc)

    def _load_structure_rules(self) -> list:
        """加载结构检测规则（带缓存）"""
        # 如果已经缓存，直接返回
        if self._structure_rules_cache is not None:
            return self._structure_rules_cache

        try:
            # 规则文件路径
            rules_file = Path(__file__).parent.parent.parent / "config" / "rules" / "hbkj_bachelor_2024.json"

            if not rules_file.exists():
                logger.warning(f"规则文件不存在: {rules_file}")
                self._structure_rules_cache = []
                return []

            with open(rules_file, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)

            # 提取document_structure部分
            document_structure = rules_data.get('definitions', {}).get('document_structure', [])
            logger.info(f"成功加载 {len(document_structure)} 个结构规则")

            # 缓存结果
            self._structure_rules_cache = document_structure
            return document_structure

        except Exception as e:
            logger.error(f"加载结构规则失败: {str(e)}")
            self._structure_rules_cache = []
            return []

    def _extract_pages_content(self, doc: Any) -> dict:
        """
        按页面提取文档内容

        Args:
            doc: Word文档对象

        Returns:
            页面内容字典 {page_number: {'paragraphs': [...], 'tables': [...]}}
        """
        try:
            pages_content = {}

            # 遍历所有段落，按页面分组
            for i in range(1, doc.Paragraphs.Count + 1):
                try:
                    paragraph = doc.Paragraphs(i)
                    text = paragraph.Range.Text.strip()

                    if not text:
                        continue

                    # 获取段落所在页面
                    page_number = self._get_paragraph_page_number(paragraph)

                    if page_number not in pages_content:
                        pages_content[page_number] = {
                            'paragraphs': [],
                            'tables': [],
                            'page_number': page_number
                        }

                    # 检查是否在表格中
                    is_in_table = self._is_paragraph_in_table(paragraph)

                    paragraph_info = {
                        'index': i,
                        'text': text,
                        'style': getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal',
                        'alignment': self._get_paragraph_alignment(paragraph),
                        'font_size': self._get_paragraph_font_size(paragraph),
                        'is_bold': self._is_paragraph_bold(paragraph),
                        'is_in_table': is_in_table
                    }

                    if is_in_table:
                        pages_content[page_number]['tables'].append(paragraph_info)
                        logger.info(f"检测到表格内容: {text[:30]}... (第{page_number}页)")
                    else:
                        pages_content[page_number]['paragraphs'].append(paragraph_info)

                except Exception as e:
                    logger.warning(f"处理段落 {i} 失败: {str(e)}")
                    continue

            logger.info(f"成功提取 {len(pages_content)} 页内容")
            return pages_content

        except Exception as e:
            logger.error(f"按页面提取内容失败: {str(e)}")
            return {}

    def _detect_structures_by_page_order(self, pages_content: dict, rules: list) -> list:
        """
        按页面顺序检测所有结构（标准和非标准）

        Args:
            pages_content: 页面内容字典
            rules: 结构规则列表

        Returns:
            按页面顺序排列的结构列表
        """
        all_structures = []

        # 识别所有封面页（包括非第一页的封面页）
        cover_pages = self._identify_all_cover_pages(pages_content)
        logger.info(f"识别到的封面页: {cover_pages}")

        # 按页面顺序遍历
        for page_num in sorted(pages_content.keys()):
            page_data = pages_content[page_num]

            # 跳过非第一页的封面页
            if page_num in cover_pages and page_num != 1:
                logger.info(f"跳过非第一页的封面页: 第{page_num}页")
                continue

            # 在当前页面检测标准结构
            page_standard_structures = self._detect_standard_structures_on_page(
                page_num, page_data, rules
            )

            # 检查当前页面是否为封面页
            is_cover_page = page_num in cover_pages

            # 如果是封面页，只保留封面结构，不检测其他非标准结构
            if is_cover_page:
                logger.info(f"第{page_num}页为封面页，跳过非标准结构检测")
                page_non_standard_structures = []
            else:
                # 在当前页面检测非标准结构
                page_non_standard_structures = self._detect_non_standard_structures_on_page(
                    page_num, page_data, page_standard_structures
                )

            # 按段落索引排序当前页面的结构
            page_structures = page_standard_structures + page_non_standard_structures
            page_structures.sort(key=lambda x: x.get('content', {}).get('paragraph_index', 0))

            # 添加到总列表
            all_structures.extend(page_structures)

        # 🔥 新增：处理需要跨页面提取的参考文献结构
        self._enhance_reference_structures_with_cross_page_content(all_structures, pages_content)

        # 🔥 新增：提取完整的结构内容（从标题到下一个标题之间的所有内容）
        self._extract_complete_structure_content(all_structures, pages_content)

        # 添加缺失的必需标准结构
        missing_structures = self._find_missing_required_structures(all_structures, rules)
        all_structures.extend(missing_structures)

        logger.info(f"按页面顺序检测完成，共 {len(all_structures)} 个结构")
        return all_structures

    def _get_paragraph_page_number(self, paragraph: Any) -> int:
        """获取段落所在页面号"""
        try:
            # 通过段落的Range获取页面信息
            if hasattr(paragraph, 'Range'):
                range_obj = paragraph.Range
                if hasattr(range_obj, 'Information'):
                    # wdActiveEndPageNumber = 3
                    page_number = range_obj.Information(3)
                    return int(page_number) if page_number else 1
            return 1
        except Exception as e:
            logger.warning(f"获取段落页面号失败: {str(e)}")
            return 1

    def _is_paragraph_in_table(self, paragraph: Any) -> bool:
        """检查段落是否在表格中"""
        try:
            if hasattr(paragraph, 'Range'):
                range_obj = paragraph.Range
                if hasattr(range_obj, 'Information'):
                    # wdWithInTable = 12
                    in_table = range_obj.Information(12)
                    return bool(in_table)
            return False
        except Exception as e:
            return False

    def _detect_standard_structures_on_page(self, page_num: int, page_data: dict, rules: list) -> list:
        """
        在指定页面检测标准结构

        Args:
            page_num: 页面号
            page_data: 页面数据
            rules: 结构规则列表

        Returns:
            该页面的标准结构列表
        """
        page_structures = []
        paragraphs = page_data.get('paragraphs', [])

        for structure_rule in rules:
            structure_name = structure_rule.get('name')
            identifiers = structure_rule.get('identifiers', [])
            required = structure_rule.get('required', False)

            # 对于关键词，使用特殊的检测方法
            if structure_name in ["中文关键词", "英文关键词"]:
                found_content = self._find_keywords_on_page(page_num, page_data, structure_name)
                if found_content:
                    # 🔥 新增：计算关键词的字数并生成count字段
                    word_count = self._count_words_in_text(found_content.get('text', ''))
                    count_display = f"{word_count}字"

                    page_structures.append({
                        'name': structure_name,
                        'status': 'present',
                        'type': 'standard',
                        'page': page_num,
                        'content': found_content,
                        'identifiers_matched': identifiers,
                        'required': required,
                        'count': count_display  # 🔥 新增：统一的count字段
                    })
                    logger.info(f"检测到结构: {structure_name} (第{page_num}页) - {count_display}")
                continue

            # 特殊处理目录结构
            if structure_name == '目录':
                toc_structure = self._detect_toc_structure_on_page(page_num, page_data, identifiers, required)
                if toc_structure:
                    page_structures.append(toc_structure)
                continue

            # 检测其他标准结构
            for i, paragraph in enumerate(paragraphs):
                text = paragraph.get('text', '').strip()
                if not text:
                    continue

                # 跳过表格内容
                is_in_table = paragraph.get('is_in_table', False)
                if is_in_table:
                    logger.debug(f"跳过表格内容: {text[:30]}... (第{page_num}页)")
                    continue

                # 跳过TOC样式的段落（目录条目不应被识别为其他结构）
                style = paragraph.get('style', 'Normal')
                if 'TOC' in style.upper() or 'toc' in style.lower():
                    logger.debug(f"跳过TOC样式段落: {text[:30]}... (第{page_num}页, 样式: {style})")
                    continue

                # 检查是否匹配标识符
                if self._matches_structure_identifiers(text, identifiers, structure_name):
                    # 验证结构特征
                    if self._validate_structure_characteristics(paragraph, structure_name):
                        # 🔥 新增：为参考文献提取完整内容
                        if structure_name == '参考文献':
                            # 首先尝试从当前页面提取
                            full_reference_text = self._extract_full_reference_content(paragraphs, i)

                            # 如果当前页面内容不足（少于3条参考文献），尝试跨页面提取
                            import re
                            current_ref_count = len(re.findall(r'\[\d+\]', full_reference_text))
                            if current_ref_count < 3:
                                # 需要访问所有页面内容，这需要在调用函数中传递
                                # 暂时使用当前页面的内容，后续会在结构处理中补充
                                logger.info(f"参考文献在当前页面只找到{current_ref_count}条，可能需要跨页面查找")

                            content = {
                                'text': full_reference_text,
                                'paragraph_index': i,
                                'alignment': paragraph.get('alignment', 'left'),
                                'font_size': paragraph.get('font_size'),
                                'style': paragraph.get('style', 'Normal'),
                                'is_bold': paragraph.get('is_bold', False),
                                'needs_cross_page_extraction': current_ref_count < 3  # 标记需要跨页面提取
                            }
                        else:
                            content = {
                                'text': text,
                                'paragraph_index': i,
                                'alignment': paragraph.get('alignment', 'left'),
                                'font_size': paragraph.get('font_size'),
                                'style': paragraph.get('style', 'Normal'),
                                'is_bold': paragraph.get('is_bold', False)
                            }

                        # 🔥 修复：计算整个结构的字数统计，而不仅仅是标识段落
                        word_count = self._calculate_structure_word_count(
                            page_data, structure_name, page_num, i
                        )

                        # 🔥 新增：为参考文献结构添加语言分类统计
                        chinese_count = 0
                        foreign_count = 0

                        if structure_name == '参考文献':
                            logger.info(f"🔥 开始处理参考文献结构")
                            # 优先使用临时存储的详细信息
                            if hasattr(self, '_temp_reference_details'):
                                chinese_count = self._temp_reference_details.get('chinese_count', 0)
                                foreign_count = self._temp_reference_details.get('foreign_count', 0)
                                logger.info(f"🔍 使用临时存储的参考文献统计: 中文{chinese_count}条, 外文{foreign_count}条")
                                # 清除临时数据
                                delattr(self, '_temp_reference_details')
                            else:
                                # 备用方案：直接分析内容
                                logger.info(f"🔍 临时存储不存在，直接分析内容")
                                logger.info(f"🔍 开始为参考文献结构计算语言分类，内容长度: {len(content['text'])}")
                                logger.info(f"🔍 参考文献内容预览: {content['text'][:200]}...")
                                chinese_count, foreign_count = self._count_references_by_language(content['text'])
                                logger.info(f"参考文献语言分类: 中文{chinese_count}条, 外文{foreign_count}条")

                        # 🔥 新增：构建统一的count字段
                        logger.info(f"🔥 构建count字段 - 结构: {structure_name}, 字数: {word_count}, 中文: {chinese_count}, 外文: {foreign_count}")
                        if structure_name == '参考文献':
                            # 参考文献结构：count表示参考文献条数（带语言分类）
                            if chinese_count > 0 and foreign_count > 0:
                                count_display = f"中文{chinese_count}条;外文{foreign_count}条"
                            elif chinese_count > 0:
                                count_display = f"中文{chinese_count}条"
                            elif foreign_count > 0:
                                count_display = f"外文{foreign_count}条"
                            else:
                                count_display = f"{chinese_count + foreign_count}条"
                            logger.info(f"🔥 参考文献count字段: {count_display}")
                        else:
                            # 其他结构：count表示字数
                            count_display = f"{word_count}字"
                            logger.info(f"🔥 普通结构count字段: {count_display}")

                        logger.info(f"🔥 最终count字段: {count_display}")

                        # 构建基础结构数据
                        structure_data = {
                            'name': structure_name,
                            'status': 'present',
                            'type': 'standard',
                            'page': page_num,
                            'content': content,
                            'identifiers_matched': identifiers,
                            'required': required,
                            'count': count_display  # 🔥 新增：统一的count字段
                        }

                        # 🔥 新增：只为参考文献结构添加语言分类字段（保留用于前端备用）
                        if structure_name == '参考文献':
                            structure_data['reference_chinese_count'] = chinese_count
                            structure_data['reference_foreign_count'] = foreign_count
                            logger.info(f"🔥 参考文献结构数据已设置: count={count_display}, chinese={chinese_count}, foreign={foreign_count}")



                        page_structures.append(structure_data)
                        logger.info(f"检测到结构: {structure_name} (第{page_num}页)")
                        break  # 找到一个就够了

        return page_structures

    def _detect_toc_structure_on_page(self, page_num: int, page_data: dict, identifiers: list, required: bool) -> dict:
        """
        检测目录结构

        Args:
            page_num: 页面号
            page_data: 页面数据
            identifiers: 目录标识符
            required: 是否必需

        Returns:
            目录结构信息，如果未检测到则返回None
        """
        paragraphs = page_data.get('paragraphs', [])

        # 1. 检查第一个段落是否包含"目录"关键词
        if not paragraphs:
            return None

        first_paragraph = paragraphs[0]
        first_text = first_paragraph.get('text', '').strip()

        # 检查是否包含目录关键词（处理全角空格等特殊字符）
        normalized_text = first_text.replace('\u3000', '').replace(' ', '').replace('\t', '')
        has_toc_keyword = any(identifier in normalized_text for identifier in identifiers)
        if not has_toc_keyword:
            return None

        # 2. 检查页面中是否有TOC样式的段落或目录特征
        toc_entries = []
        has_toc_style = False
        has_toc_pattern = False

        for i, paragraph in enumerate(paragraphs):
            style = paragraph.get('style', 'Normal')
            text = paragraph.get('text', '').strip()

            # 检查是否为TOC样式
            if 'TOC' in style.upper() or 'toc' in style.lower():
                has_toc_style = True
                if text:  # 记录TOC条目
                    toc_entries.append({
                        'text': text,
                        'paragraph_index': i,
                        'style': style
                    })

            # 检查是否有目录模式（章节号 + 标题 + 页码）
            if text and i > 0:  # 跳过标题行
                # 匹配模式：第X章、X.X、数字等开头的行
                import re
                if re.match(r'^(第\s*\d+\s*章|第\s*[一二三四五六七八九十]+\s*章|\d+\.?\d*|\d+\.\d+)', text):
                    has_toc_pattern = True
                    toc_entries.append({
                        'text': text,
                        'paragraph_index': i,
                        'style': style
                    })

        # 3. 满足关键词且有TOC样式或目录模式就认为是目录
        if has_toc_keyword and (has_toc_style or has_toc_pattern):
            # 存储TOC信息供后续正文检测使用
            if not hasattr(self, '_toc_info'):
                self._toc_info = {}

            self._toc_info[page_num] = {
                'entries': toc_entries,
                'first_entry_text': toc_entries[0]['text'] if toc_entries else None
            }

            content = {
                'text': first_text,
                'paragraph_index': 0,
                'alignment': first_paragraph.get('alignment', 'center'),
                'font_size': first_paragraph.get('font_size'),
                'style': first_paragraph.get('style', 'Normal'),
                'is_bold': first_paragraph.get('is_bold', False),
                'toc_entries_count': len(toc_entries),
                'detection_method': 'toc_style' if has_toc_style else 'pattern_match'
            }

            # 🔥 新增：计算目录的字数并生成count字段
            word_count = self._count_words_in_text(first_text)
            count_display = f"{word_count}字"

            first_entry_text = toc_entries[0]['text'] if toc_entries else "无"
            logger.info(f"检测到目录结构 (第{page_num}页): 包含{len(toc_entries)}个条目，检测方式: {'TOC样式' if has_toc_style else '模式匹配'}，第一条: '{first_entry_text}' - {count_display}")

            return {
                'name': '目录',
                'status': 'present',
                'type': 'standard',
                'page': page_num,
                'content': content,
                'identifiers_matched': identifiers,
                'required': required,
                'count': count_display  # 🔥 新增：统一的count字段
            }

        return None

    def _detect_non_standard_structures_on_page(self, page_num: int, page_data: dict,
                                               page_standard_structures: list) -> list:
        """
        在指定页面检测非标准结构

        🔥 重要限制：只对每页的第一个有效段落进行非标准结构识别

        Args:
            page_num: 页面号
            page_data: 页面数据
            page_standard_structures: 该页面已检测到的标准结构

        Returns:
            该页面的非标准结构列表
        """
        page_non_standard_structures = []
        paragraphs = page_data.get('paragraphs', [])

        # 获取已检测的标准结构的段落索引
        used_paragraph_indices = set()
        for structure in page_standard_structures:
            content = structure.get('content', {})
            paragraph_index = content.get('paragraph_index')
            if paragraph_index is not None:
                used_paragraph_indices.add(paragraph_index)

        # 🔥 新增：只检测每页的第一个有效段落
        first_valid_paragraph_found = False

        # 检测非标准结构
        for i, paragraph in enumerate(paragraphs):
            # 跳过已被标准结构使用的段落
            if i in used_paragraph_indices:
                continue

            text = paragraph.get('text', '').strip()
            if not text:
                continue

            # 跳过表格内容
            is_in_table = paragraph.get('is_in_table', False)
            if is_in_table:
                logger.debug(f"跳过表格内容: {text[:30]}... (第{page_num}页)")
                continue

            # 跳过标题样式的内容（这些通常对应目录中的条目）
            style = paragraph.get('style', 'Normal')
            if self._is_heading_style(style):
                logger.debug(f"跳过标题样式内容: {text[:30]}... (第{page_num}页, 样式: {style})")
                continue

            # 🔥 新增：如果已经找到第一个有效段落，则跳过后续段落
            if first_valid_paragraph_found:
                logger.debug(f"跳过非首段落: {text[:30]}... (第{page_num}页, 段落索引: {i})")
                break

            # 标记已找到第一个有效段落
            first_valid_paragraph_found = True

            alignment = paragraph.get('alignment', 'left')
            font_size = paragraph.get('font_size')
            is_bold = paragraph.get('is_bold', False)

            # 检查是否符合非标准结构特征
            if self._is_non_standard_structure(text, alignment, font_size, is_bold, page_non_standard_structures):
                structure_name = self._generate_non_standard_structure_name(text)

                content = {
                    'text': text,
                    'paragraph_index': i,
                    'alignment': alignment,
                    'font_size': font_size,
                    'style': paragraph.get('style', 'Normal'),
                    'is_bold': is_bold
                }

                # 🔥 新增：计算非标准结构的字数统计
                word_count = self._count_words_in_text(text)
                count_display = f"{word_count}字"

                page_non_standard_structures.append({
                    'name': structure_name,
                    'status': 'present',
                    'type': 'non_standard',  # 🔥 修复：使用下划线格式，前端会统一处理
                    'page': page_num,
                    'content': content,
                    'identifiers_matched': [],
                    'required': False,
                    'count': count_display  # 🔥 新增：统一的count字段
                })

                logger.info(f"检测到非标准结构(首段落): {structure_name} (第{page_num}页) - {text[:30]}...")
            else:
                logger.debug(f"首段落不符合非标准结构特征: {text[:30]}... (第{page_num}页)")

        return page_non_standard_structures

    def _find_keywords_on_page(self, page_num: int, page_data: dict, keyword_type: str) -> dict:
        """
        在指定页面查找关键词

        Args:
            page_num: 页面号
            page_data: 页面数据
            keyword_type: 关键词类型（中文关键词或英文关键词）

        Returns:
            关键词内容字典，如果未找到则返回None
        """
        paragraphs = page_data.get('paragraphs', [])

        if keyword_type == "中文关键词":
            patterns = [
                r'关键词[：:]\s*(.+)',
                r'关键字[：:]\s*(.+)',
                r'Keywords?\s*[：:]\s*(.+)',
                r'Key\s*words?\s*[：:]\s*(.+)'
            ]
        else:  # 英文关键词
            patterns = [
                r'Key\s*words?\s*[：:]\s*(.+)',
                r'Keywords?\s*[：:]\s*(.+)'
            ]

        import re
        for i, paragraph in enumerate(paragraphs):
            text = paragraph.get('text', '').strip()

            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    keywords_text = match.group(1).strip()

                    # 验证关键词内容
                    if keyword_type == "中文关键词" and self._contains_chinese(keywords_text):
                        return {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': paragraph.get('alignment', 'left'),
                            'font_size': paragraph.get('font_size'),
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': paragraph.get('is_bold', False)
                        }
                    elif keyword_type == "英文关键词" and self._contains_english(keywords_text):
                        return {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': paragraph.get('alignment', 'left'),
                            'font_size': paragraph.get('font_size'),
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': paragraph.get('is_bold', False)
                        }

        return None

    def _contains_chinese(self, text: str) -> bool:
        """
        检查文本是否包含中文字符

        Args:
            text: 要检查的文本

        Returns:
            是否包含中文字符
        """
        # 使用预编译的正则表达式
        return bool(COMPILED_PATTERNS['chinese_chars'].search(text))

    def _contains_english(self, text: str) -> bool:
        """
        检查文本是否包含英文字符

        Args:
            text: 要检查的文本

        Returns:
            是否包含英文字符
        """
        # 使用预编译的正则表达式
        return bool(COMPILED_PATTERNS['english_chars'].search(text))

    def _find_missing_required_structures(self, detected_structures: list, rules: list) -> list:
        """
        查找缺失的必需结构

        Args:
            detected_structures: 已检测到的结构列表
            rules: 结构规则列表

        Returns:
            缺失的必需结构列表
        """
        missing_structures = []
        detected_names = {s['name'] for s in detected_structures if s['status'] == 'present'}

        for rule in rules:
            structure_name = rule.get('name')
            required = rule.get('required', False)

            if required and structure_name not in detected_names:
                missing_structures.append({
                    'name': structure_name,
                    'status': 'missing',
                    'type': 'standard',
                    'required': required,
                    'count': '0字'  # 🔥 新增：缺失结构的count字段
                })
                logger.warning(f"缺失必需结构: {structure_name}")

        return missing_structures

    def _find_structure_in_pages(self, pages_content: dict, identifiers: list, start_page: int, structure_name: str) -> tuple:
        """
        在页面中查找特定结构

        Args:
            pages_content: 页面内容字典
            identifiers: 结构标识符列表
            start_page: 开始查找的页面
            structure_name: 结构名称

        Returns:
            (found_page, found_content) 或 (None, None)
        """
        try:
            # 封面特殊处理：只检查第一页的所有内容
            if structure_name == "封面":
                return self._find_cover_page(pages_content, identifiers)

            # 其他结构按原逻辑查找
            for page_num in sorted(pages_content.keys()):
                if page_num < start_page:
                    continue

                page_data = pages_content[page_num]

                # 只检查非表格段落
                for paragraph in page_data['paragraphs']:
                    text = paragraph['text']
                    alignment = paragraph.get('alignment', 'left')
                    font_size = paragraph.get('font_size')

                    # 检查是否匹配结构标识符
                    if self._matches_structure_identifiers(text, identifiers, structure_name):
                        # 额外验证：检查是否符合结构特征
                        if self._validate_structure_characteristics(paragraph, structure_name):
                            logger.info(f"在第{page_num}页找到结构 '{structure_name}': {text[:50]}...")
                            return page_num, {
                                'text': text,
                                'paragraph_index': paragraph['index'],
                                'alignment': alignment,
                                'font_size': font_size,
                                'style': paragraph.get('style', 'Normal')
                            }

            return None, None

        except Exception as e:
            logger.error(f"查找结构 {structure_name} 失败: {str(e)}")
            return None, None

    def _find_cover_page(self, pages_content: dict, identifiers: list) -> tuple:
        """
        专门查找封面页（第一页）

        Args:
            pages_content: 页面内容字典
            identifiers: 封面标识符列表

        Returns:
            (found_page, found_content) 或 (None, None)
        """
        try:
            # 只检查第一页
            if 1 not in pages_content:
                logger.warning("第一页内容不存在")
                return None, None

            page_data = pages_content[1]

            # 收集第一页的所有文本内容
            all_text_parts = []
            representative_paragraph = None

            # 收集所有非表格段落的文本
            for paragraph in page_data['paragraphs']:
                text = paragraph['text'].strip()
                if text:
                    all_text_parts.append(text)
                    # 选择一个代表性段落（优先选择包含"学位论文"的）
                    if not representative_paragraph or '学位论文' in text:
                        representative_paragraph = paragraph

            # 合并所有文本进行检测
            combined_text = ' '.join(all_text_parts)

            # 使用封面检测逻辑
            if self._is_cover_page_content(combined_text, identifiers):
                # 选择最具代表性的文本作为返回内容
                if representative_paragraph:
                    display_text = representative_paragraph['text']
                else:
                    # 如果没有找到代表性段落，使用第一个非空段落
                    display_text = all_text_parts[0] if all_text_parts else "封面页"

                logger.info(f"在第1页找到结构 '封面': {display_text[:50]}...")

                return 1, {
                    'text': display_text,
                    'paragraph_index': representative_paragraph['index'] if representative_paragraph else 1,
                    'alignment': representative_paragraph.get('alignment', 'center') if representative_paragraph else 'center',
                    'font_size': representative_paragraph.get('font_size') if representative_paragraph else None,
                    'style': representative_paragraph.get('style', 'Normal') if representative_paragraph else 'Normal',
                    'combined_text': combined_text[:200] + '...' if len(combined_text) > 200 else combined_text
                }

            return None, None

        except Exception as e:
            logger.error(f"查找封面页失败: {str(e)}")
            return None, None

    def _matches_structure_identifiers(self, text: str, identifiers: list, structure_name: str) -> bool:
        """检查文本是否匹配结构标识符"""
        # 标准化文本：去除所有空格并转为小写
        text_normalized = ''.join(text.split()).lower()

        # 对于封面，使用特殊的检测逻辑
        if structure_name == "封面":
            return self._is_cover_page_content(text, identifiers)

        # 对于正文，使用特殊的检测逻辑
        if structure_name == "正文":
            logger.info(f"正在检测正文结构：文本='{text[:50]}...', 标识符={identifiers}")
            result = self._is_main_content(text, identifiers)
            logger.info(f"正文结构检测结果：{result}")
            return result

        # 注意：关键词检测现在由 _find_keywords_in_document 方法独立处理
        # 这里不再处理关键词检测，避免重复逻辑

        # 对于其他结构，匹配任一标识符即可
        for identifier in identifiers:
            # 标准化标识符：去除所有空格并转为小写
            identifier_normalized = ''.join(identifier.split()).lower()
            if identifier_normalized in text_normalized:
                return True

        return False

    def _is_cover_page_content(self, text: str, identifiers: list) -> bool:
        """
        专门检测封面页内容

        封面页必须包含核心的学位论文标识，而不是任务书、开题报告等其他页面

        Args:
            text: 文本内容
            identifiers: 封面标识符列表

        Returns:
            是否为封面内容
        """
        # 核心封面标识（必须包含其中之一）
        core_cover_identifiers = [
            '学士学位论文',
            '硕士学位论文',
            '博士学位论文',
            '学位论文'
        ]

        # 排除标识（如果包含这些，则不是封面）
        exclusion_identifiers = [
            '任务书',
            '开题报告',
            '诚信声明',
            '原创性声明',
            '版权声明',
            '摘要',
            '目录',
            '参考文献',
            '致谢',
            '附录'
        ]

        # 首先检查排除条件
        for exclusion in exclusion_identifiers:
            if exclusion in text:
                return False

        # 必须包含核心封面标识
        has_core_identifier = any(core_id in text for core_id in core_cover_identifiers)
        if not has_core_identifier:
            return False

        # 检查是否包含规则中定义的标识符
        matched_identifiers = sum(1 for identifier in identifiers if identifier in text)

        # 如果包含核心封面标识，只需要1个标识符即可
        if any(core_id in text for core_id in core_cover_identifiers):
            return matched_identifiers >= 1

        # 否则至少匹配2个规则标识符
        return matched_identifiers >= 2

    def _is_main_content(self, text: str, identifiers: list) -> bool:
        """
        检测是否为正文内容

        正文检测逻辑：
        1. 首先检查是否匹配正文标识符
        2. 确保是正文标题而不是正文内容
        3. 避免将正文段落误识别为正文结构

        Args:
            text: 文本内容
            identifiers: 正文标识符

        Returns:
            是否为正文内容
        """
        # 标准化文本
        text_normalized = ''.join(text.split()).lower()

        logger.info(f"正文检测开始：文本='{text[:50]}...', 标识符={identifiers}")

        # 1. 检查是否匹配正文标识符
        matches_identifier = False
        matched_identifier = None
        for identifier in identifiers:
            identifier_normalized = ''.join(identifier.split()).lower()
            if identifier_normalized in text_normalized:
                matches_identifier = True
                matched_identifier = identifier
                break

        logger.info(f"正文检测：标识符匹配结果={matches_identifier}, 匹配的标识符='{matched_identifier}'")

        # 如果不匹配标识符，直接返回False
        if not matches_identifier:
            return False

        # 2. 进一步检查是否为正文标题（而不是正文内容）
        if not self._is_main_content_title(text, matched_identifier):
            logger.info(f"正文检测：文本 '{text[:50]}...' 包含正文标识符但不是正文标题，跳过")
            return False

        logger.info(f"正文检测：匹配正文标识符 '{matched_identifier}'，确认为正文标题")
        return True

    def _is_main_content_title(self, text: str, matched_identifier: str) -> bool:
        """
        判断匹配正文标识符的文本是否为正文标题（而不是正文内容）

        Args:
            text: 文本内容
            matched_identifier: 匹配的正文标识符

        Returns:
            是否为正文标题
        """
        # 清理文本
        clean_text = text.strip()

        # 1. 如果文本过长（超过50字符），很可能是正文段落而不是标题
        if len(clean_text) > 50:
            return False

        # 2. 如果文本就是标识符本身或包含标识符的短标题，认为是正文标题
        # 例如："绪论"、"第一章 绪论"、"引言"等
        text_normalized = ''.join(clean_text.split()).lower()
        identifier_normalized = ''.join(matched_identifier.split()).lower()

        # 如果文本就是标识符，肯定是标题
        if text_normalized == identifier_normalized:
            return True

        # 如果文本以标识符开头且长度合理，可能是标题
        if text_normalized.startswith(identifier_normalized) and len(clean_text) <= 30:
            return True

        # 如果文本包含"第X章"、"第X节"等模式，可能是标题
        import re
        chapter_patterns = [
            r'第[一二三四五六七八九十\d]+章',
            r'第[一二三四五六七八九十\d]+节',
            r'第[一二三四五六七八九十\d]+部分',
        ]

        for pattern in chapter_patterns:
            if re.search(pattern, clean_text):
                return True

        # 其他情况认为是正文内容
        return False

    def _find_keywords_in_document(self, pages_content: dict, structure_name: str) -> tuple:
        """
        在整个文档中独立查找关键词

        Args:
            pages_content: 页面内容字典
            structure_name: 结构名称（"中文关键词" 或 "英文关键词"）

        Returns:
            tuple: (页面号, 内容) 或 (None, None)
        """
        logger.info(f"开始全文搜索关键词: {structure_name}")

        # 定义关键词检测模式
        if structure_name == "中文关键词":
            # 中文关键词模式：关键词：xxx 或 关键字：xxx
            pattern = r'^(关键词|关键字)\s*[：:]\s*(.+)'
        elif structure_name == "英文关键词":
            # 英文关键词模式：Keywords: xxx 或 Key words: xxx
            pattern = r'^(keywords?|key\s+words?)\s*[：:]\s*(.+)'
        else:
            return None, None

        # 遍历所有页面查找关键词
        for page_num, page_data in pages_content.items():
            paragraphs = page_data.get('paragraphs', [])

            for paragraph in paragraphs:
                text = paragraph.get('text', '').strip()

                if not text:
                    continue

                # 使用正则表达式匹配关键词模式
                flags = re.IGNORECASE if structure_name == "英文关键词" else 0
                match = re.match(pattern, text, flags)

                if match:
                    keywords_content = match.group(2).strip()

                    # 确保关键词内容不为空且有实际内容
                    if keywords_content and len(keywords_content) > 2:
                        logger.info(f"在第{page_num}页找到{structure_name}: {text[:50]}...")
                        return page_num, text

        logger.warning(f"未找到{structure_name}")
        return None, None

    # 注意：此方法已被 _detect_structures_by_page_order 中的按页面检测逻辑替代
    # 保留此方法仅为兼容性，实际不再使用
    def _detect_non_standard_structures(self, pages_content: dict, detected_structures: list) -> list:
        """
        检测非标准结构（规则文件中未定义的结构）

        ⚠️ 已废弃：此方法已被新的按页面顺序检测逻辑替代

        检测标准：
        1. 文字居中对齐
        2. 字号大于正文（通常 >= 14pt）
        3. 文本长度适中（不是正文段落）
        4. 不与已检测的标准结构重复
        5. 排除非第一页的封面页

        Args:
            pages_content: 页面内容字典
            detected_structures: 已检测到的标准结构列表

        Returns:
            非标准结构列表
        """
        non_standard_structures = []

        # 获取已检测结构的页面和段落索引，避免重复
        detected_positions = set()
        for structure in detected_structures:
            if structure['status'] == 'present':
                page = structure.get('page')
                content = structure.get('content', {})
                paragraph_index = content.get('paragraph_index', 0)
                if page:
                    detected_positions.add((page, paragraph_index))

        # 识别所有封面页（包括非第一页的封面页）
        cover_pages = self._identify_all_cover_pages(pages_content)

        logger.info("开始检测非标准结构...")
        logger.info(f"识别到的封面页: {cover_pages}")

        # 遍历所有页面查找非标准结构
        for page_num, page_data in pages_content.items():
            # 跳过封面页（除了第一页，其他封面页完全跳过）
            if page_num in cover_pages and page_num != 1:
                logger.info(f"跳过非第一页的封面页: 第{page_num}页")
                continue

            paragraphs = page_data.get('paragraphs', [])

            for i, paragraph in enumerate(paragraphs):
                # 跳过已检测的标准结构
                if (page_num, i) in detected_positions:
                    continue

                text = paragraph.get('text', '').strip()
                alignment = paragraph.get('alignment', 'left')
                font_size = paragraph.get('font_size')
                is_bold = paragraph.get('is_bold', False)

                # 检查是否符合非标准结构特征
                if self._is_non_standard_structure(text, alignment, font_size, is_bold, detected_structures):
                    # 生成结构名称
                    structure_name = self._generate_non_standard_structure_name(text)

                    non_standard_structures.append({
                        'name': structure_name,
                        'status': 'present',
                        'type': 'non_standard',
                        'page': page_num,
                        'content': {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': alignment,
                            'font_size': font_size,
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': is_bold
                        },
                        'identifiers_matched': [],
                        'required': False
                    })

                    logger.info(f"检测到非标准结构: {structure_name} (第{page_num}页) - {text[:30]}...")

        logger.info(f"检测到 {len(non_standard_structures)} 个非标准结构")
        return non_standard_structures

    def _identify_all_cover_pages(self, pages_content: dict) -> set:
        """
        识别文档中的所有封面页（包括非第一页的封面页）

        封面页特征：
        1. 包含学位论文标题关键词
        2. 包含学生信息（姓名、学号等）
        3. 包含学校、专业信息
        4. 包含日期信息
        5. 整体布局与第一页相似

        Args:
            pages_content: 页面内容字典

        Returns:
            封面页页码集合
        """
        cover_pages = set()

        # 第一页默认为封面页
        if 1 in pages_content:
            cover_pages.add(1)

        # 从第一页提取封面特征信息
        first_page_features = self._extract_cover_features(pages_content.get(1, {}))

        # 检查其他页面是否为封面页
        for page_num, page_data in pages_content.items():
            if page_num == 1:  # 跳过第一页
                continue

            # 提取当前页面特征
            current_page_features = self._extract_cover_features(page_data)

            # 计算与第一页的相似度
            similarity = self._calculate_cover_similarity(first_page_features, current_page_features)

            # 如果相似度超过阈值，认为是封面页
            # 提高阈值到85%，确保只有真正相同的封面页才被识别
            if similarity >= 0.85:  # 85%相似度阈值
                cover_pages.add(page_num)
                logger.info(f"识别到封面页: 第{page_num}页 (相似度: {similarity:.2f})")
            else:
                logger.debug(f"第{page_num}页相似度不足: {similarity:.2f} < 0.85")

        return cover_pages

    def _extract_cover_features(self, page_data: dict) -> dict:
        """
        从页面中提取封面特征

        Args:
            page_data: 页面数据

        Returns:
            封面特征字典
        """
        features = {
            'has_degree_title': False,      # 是否有学位论文标题
            'has_student_info': False,      # 是否有学生信息
            'has_school_info': False,       # 是否有学校信息
            'has_date_info': False,         # 是否有日期信息
            'has_thesis_title': False,      # 是否有论文题目
            'all_texts': [],                # 所有文本内容（用于精确匹配）
            'text_with_format': [],         # 文本及其格式信息
            'centered_texts': [],           # 居中文本列表
            'large_font_texts': [],         # 大字号文本列表
            'total_paragraphs': 0           # 总段落数
        }

        paragraphs = page_data.get('paragraphs', [])
        features['total_paragraphs'] = len(paragraphs)

        # 封面关键词
        degree_keywords = ['学士学位论文', '硕士学位论文', '博士学位论文', '毕业论文', '毕业设计']
        student_keywords = ['姓名', '学号', '学生', '专业', '班级']
        school_keywords = ['学院', '大学', '学校', '系', '院系']
        date_keywords = ['年', '月', '日', '时间', '日期']

        for paragraph in paragraphs:
            text = paragraph.get('text', '').strip()
            alignment = paragraph.get('alignment', 'left')
            font_size = paragraph.get('font_size', 0)

            if not text:
                continue

            # 收集所有文本内容
            features['all_texts'].append(text)

            # 收集文本及其格式信息
            features['text_with_format'].append({
                'text': text,
                'alignment': alignment,
                'font_size': font_size,
                'is_bold': paragraph.get('is_bold', False)
            })

            # 检查学位论文标题
            if any(keyword in text for keyword in degree_keywords):
                features['has_degree_title'] = True

            # 检查学生信息
            if any(keyword in text for keyword in student_keywords):
                features['has_student_info'] = True

            # 检查学校信息
            if any(keyword in text for keyword in school_keywords):
                features['has_school_info'] = True

            # 检查日期信息
            if any(keyword in text for keyword in date_keywords):
                features['has_date_info'] = True

            # 检查是否为论文题目（通常较长且居中）
            if alignment == 'center' and len(text) > 10 and len(text) < 50:
                features['has_thesis_title'] = True

            # 收集居中文本
            if alignment == 'center':
                features['centered_texts'].append(text)

            # 收集大字号文本
            if font_size and font_size >= 16:
                features['large_font_texts'].append(text)

        return features

    def _calculate_cover_similarity(self, features1: dict, features2: dict) -> float:
        """
        计算两个页面封面特征的相似度

        更严格的相似度计算：
        1. 文本内容完全匹配权重最高 (50%)
        2. 文本格式完全匹配权重较高 (30%)
        3. 关键特征匹配权重较低 (20%)

        Args:
            features1: 第一个页面的特征
            features2: 第二个页面的特征

        Returns:
            相似度分数 (0-1)
        """
        if not features1 or not features2:
            return 0.0

        score = 0.0
        total_weight = 0.0

        # 1. 文本内容完全匹配 (权重50%)
        text_similarity = self._calculate_exact_text_similarity(
            features1.get('all_texts', []),
            features2.get('all_texts', [])
        )
        text_weight = 0.50
        score += text_similarity * text_weight
        total_weight += text_weight

        # 2. 文本格式完全匹配 (权重30%)
        format_similarity = self._calculate_format_similarity(
            features1.get('text_with_format', []),
            features2.get('text_with_format', [])
        )
        format_weight = 0.30
        score += format_similarity * format_weight
        total_weight += format_weight

        # 3. 关键特征匹配 (权重20%)
        key_features = [
            ('has_degree_title', 0.05),     # 学位论文标题
            ('has_student_info', 0.05),     # 学生信息
            ('has_school_info', 0.03),      # 学校信息
            ('has_date_info', 0.03),        # 日期信息
            ('has_thesis_title', 0.04)      # 论文题目
        ]

        # 计算关键特征相似度
        for feature, weight in key_features:
            total_weight += weight
            if features1.get(feature, False) and features2.get(feature, False):
                score += weight

        return score / total_weight if total_weight > 0 else 0.0

    def _calculate_exact_text_similarity(self, texts1: list, texts2: list) -> float:
        """
        计算文本内容的精确匹配相似度

        Args:
            texts1: 第一个页面的文本列表
            texts2: 第二个页面的文本列表

        Returns:
            相似度分数 (0-1)
        """
        if not texts1 or not texts2:
            return 0.0

        # 清理文本
        clean_texts1 = [text.strip() for text in texts1 if text.strip()]
        clean_texts2 = [text.strip() for text in texts2 if text.strip()]

        if not clean_texts1 or not clean_texts2:
            return 0.0

        # 计算完全匹配的文本数量
        set1 = set(clean_texts1)
        set2 = set(clean_texts2)

        # 交集 / 并集
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _calculate_format_similarity(self, format_data1: list, format_data2: list) -> float:
        """
        计算文本格式的相似度

        Args:
            format_data1: 第一个页面的格式数据
            format_data2: 第二个页面的格式数据

        Returns:
            相似度分数 (0-1)
        """
        if not format_data1 or not format_data2:
            return 0.0

        # 创建格式签名
        def create_format_signature(format_data):
            signatures = []
            for item in format_data:
                text = item.get('text', '').strip()
                if text:
                    signature = (
                        text,
                        item.get('alignment', 'left'),
                        item.get('font_size', 0),
                        item.get('is_bold', False)
                    )
                    signatures.append(signature)
            return set(signatures)

        sig1 = create_format_signature(format_data1)
        sig2 = create_format_signature(format_data2)

        if not sig1 or not sig2:
            return 0.0

        # 计算格式签名的相似度
        intersection = len(sig1.intersection(sig2))
        union = len(sig1.union(sig2))

        return intersection / union if union > 0 else 0.0

    def _calculate_text_list_similarity(self, list1: list, list2: list) -> float:
        """
        计算两个文本列表的相似度

        Args:
            list1: 第一个文本列表
            list2: 第二个文本列表

        Returns:
            相似度分数 (0-1)
        """
        if not list1 or not list2:
            return 0.0

        # 计算交集
        set1 = set(text.strip() for text in list1 if text.strip())
        set2 = set(text.strip() for text in list2 if text.strip())

        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _is_non_standard_structure(self, text: str, alignment: str, font_size: float, is_bold: bool,
                                  existing_structures: list = None) -> bool:
        """
        判断是否为非标准结构（优化版本）

        Args:
            text: 文本内容
            alignment: 对齐方式
            font_size: 字号
            is_bold: 是否粗体
            existing_structures: 已存在的结构列表，用于避免重复

        Returns:
            是否为非标准结构
        """
        # 基本条件检查
        if not text or len(text.strip()) == 0:
            return False

        # 🔥 恢复原有的文本长度阈值，保持对非标准结构的敏感性
        if len(text) > 100:  # 恢复到100，避免过度过滤
            return False

        # 🔥 恢复原有的最小长度要求
        if len(text) < 2:  # 恢复到2
            return False

        # 过滤掉纯数字或特殊字符
        if text.isdigit() or text in ['\r', '\n', '\t', ' ']:
            return False

        # 过滤掉表图注释（这些不应该被识别为独立结构）
        if self._is_table_or_figure_caption(text):
            return False

        # 🔥 新增：检查是否与已存在的结构重复
        if existing_structures and self._is_duplicate_structure(text, existing_structures):
            return False

        # 检查格式特征
        is_centered = alignment == 'center'
        # 🔥 恢复原有的字号阈值
        is_large_font = font_size and font_size >= 14.0  # 恢复到14.0
        is_title_like = self._is_title_like_text(text)

        # 🔥 恢复原有的识别条件，保持对非标准结构的敏感性
        # 满足以下条件之一即认为是非标准结构：
        # 1. 居中且字号较大
        # 2. 居中且粗体
        # 3. 字号很大（>=16pt）
        # 4. 具有标题特征且居中
        return (
            (is_centered and is_large_font) or
            (is_centered and is_bold) or
            (font_size and font_size >= 16.0) or
            (is_title_like and is_centered)
        )

    def _is_duplicate_structure(self, text: str, existing_structures: list) -> bool:
        """
        检查是否与已存在的结构重复

        Args:
            text: 待检查的文本
            existing_structures: 已存在的结构列表

        Returns:
            是否重复
        """
        normalized_text = ''.join(text.split()).lower()

        for structure in existing_structures:
            if structure.get('status') != 'present':
                continue

            existing_text = structure.get('content', {}).get('text', '')
            existing_normalized = ''.join(existing_text.split()).lower()

            # 检查文本相似度
            if normalized_text == existing_normalized:
                return True

            # 检查是否为子串关系
            if len(normalized_text) > 5 and (
                normalized_text in existing_normalized or
                existing_normalized in normalized_text
            ):
                return True

        return False

    def _is_table_or_figure_caption(self, text: str) -> bool:
        """
        判断文本是否为表格或图片注释

        Args:
            text: 文本内容

        Returns:
            是否为表图注释
        """
        # 标准化文本（去除空格）
        normalized_text = ''.join(text.split())

        # 使用预编译的正则表达式检查是否匹配表格或图片注释模式
        for pattern in COMPILED_PATTERNS['table_figure']:
            if pattern.match(normalized_text):
                return True

        return False

    def _is_heading_style(self, style: str) -> bool:
        """
        判断样式是否为标题样式

        Args:
            style: 段落样式

        Returns:
            是否为标题样式
        """
        if not style:
            return False

        # 标准化样式名称
        style_lower = style.lower()

        # 标题样式模式
        heading_patterns = [
            '标题 1', '标题 2', '标题 3', '标题 4', '标题 5', '标题 6',
            'heading 1', 'heading 2', 'heading 3', 'heading 4', 'heading 5', 'heading 6',
            'title 1', 'title 2', 'title 3', 'title 4', 'title 5', 'title 6',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
        ]

        # 检查是否匹配标题样式
        for pattern in heading_patterns:
            if pattern.lower() in style_lower:
                return True

        return False

    def _is_title_like_text(self, text: str) -> bool:
        """
        判断文本是否具有标题特征

        Args:
            text: 文本内容

        Returns:
            是否具有标题特征
        """
        # 标题关键词
        title_keywords = [
            '第', '章', '节', '部分', '附件', '附表', '图', '表',
            '声明', '说明', '须知', '注意', '提示', '警告',
            '总结', '结论', '建议', '展望', '前言', '序言',
            '概述', '简介', '背景', '意义', '目的', '方法',
            '分析', '研究', '探讨', '讨论', '评价', '总结'
        ]

        # 检查是否包含标题关键词
        for keyword in title_keywords:
            if keyword in text:
                return True

        # 检查是否为章节编号格式
        import re
        chapter_patterns = [
            r'^第[一二三四五六七八九十\d]+章',
            r'^第[一二三四五六七八九十\d]+节',
            r'^[一二三四五六七八九十\d]+[、.]',
            r'^\d+\.\d+',
            r'^\(\d+\)',
            r'^[A-Z]\.',
        ]

        for pattern in chapter_patterns:
            if re.match(pattern, text.strip()):
                return True

        return False

    def _generate_non_standard_structure_name(self, text: str) -> str:
        """
        为非标准结构生成名称

        Args:
            text: 文本内容

        Returns:
            结构名称
        """
        # 清理文本
        clean_text = text.strip().replace('\r', '').replace('\n', '').replace('\t', ' ')

        # 如果文本较短，直接使用原始内容
        if len(clean_text) <= 20:
            return clean_text

        # 如果文本较长，截取前20个字符
        return f"{clean_text[:20]}..."

    def _validate_structure_characteristics(self, paragraph: dict, structure_name: str) -> bool:
        """验证段落是否符合特定结构的特征"""
        text = paragraph['text']
        alignment = paragraph.get('alignment', 'left')
        font_size = paragraph.get('font_size')

        # 封面特征：通常居中，字体较大
        if structure_name == "封面":
            return True  # 封面检测已经在标识符匹配中处理

        # 标题类结构：通常居中对齐，字体较大
        title_structures = ["任务书", "开题报告", "诚信声明", "版权声明", "中文摘要", "英文摘要", "目录", "致谢", "附录"]
        if structure_name in title_structures:
            # 检查是否居中且字体较大，或者文本较短（可能是标题）
            is_centered = alignment == 'center'
            is_large_font = font_size and font_size >= 14
            is_short_text = len(text) <= 50

            return is_centered or is_large_font or is_short_text

        # 其他结构：基本匹配即可
        return True

    def _generate_outline_from_structures(self, detected_structures: list, doc: Any) -> list:
        """
        从检测到的结构生成outline格式

        Args:
            detected_structures: 检测到的结构列表
            doc: Word文档对象

        Returns:
            outline列表
        """
        outline = []

        for structure in detected_structures:
            if structure['status'] == 'present':
                content = structure.get('content', {})
                structure_type = structure.get('type', 'standard')

                outline_item = {
                    'paragraph_index': content.get('paragraph_index', 0),
                    'text': content.get('text', structure['name']),
                    'style': content.get('style', 'Normal'),
                    'level': self._get_structure_level(structure['name'], structure_type),
                    'type': structure_type,
                    'alignment': content.get('alignment', 'left'),
                    'page': structure.get('page', 1),
                    'structure_name': structure['name'],
                    'font_size': content.get('font_size'),
                    'is_bold': content.get('is_bold', False)
                }
                outline.append(outline_item)

        return outline

    def _enhance_document_structures(self, detected_structures: list) -> list:
        """
        增强document_structures，添加level等字段，替代outline功能

        Args:
            detected_structures: 检测到的文档结构列表

        Returns:
            增强后的文档结构列表
        """
        enhanced_structures = []

        for structure in detected_structures:
            # 复制原有结构
            enhanced_structure = structure.copy()

            # 添加level字段（从outline逻辑移植）
            structure_name = structure.get('name', '')
            structure_type = structure.get('type', 'standard')
            enhanced_structure['level'] = self._get_structure_level(structure_name, structure_type)

            # 确保content字段包含所有必要信息
            content = enhanced_structure.get('content', {})
            if 'level' not in content:
                content['level'] = enhanced_structure['level']

            # 添加其他outline中的字段到content
            content.update({
                'structure_name': structure_name,
                'font_size': content.get('font_size'),
                'is_bold': content.get('is_bold', False),
                'alignment': content.get('alignment', 'left')
            })

            enhanced_structure['content'] = content
            enhanced_structures.append(enhanced_structure)

        return enhanced_structures

    def _get_structure_level(self, structure_name: str, structure_type: str = 'standard') -> int:
        """根据结构名称和类型获取标题级别"""

        # 标准结构的级别映射
        if structure_type == 'standard':
            level_map = {
                '封面': 0,
                '任务书': 1,
                '开题报告': 1,
                '诚信声明': 1,
                '版权声明': 1,
                '中文摘要': 1,
                '中文关键词': 2,
                '英文摘要': 1,
                '英文关键词': 2,
                '目录': 1,
                '正文': 1,
                '参考文献': 1,
                '致谢': 1,
                '附录': 1
            }
            return level_map.get(structure_name, 1)

        # 非标准结构的级别判断
        elif structure_type == 'non_standard':
            # 根据文本内容判断级别
            if '第' in structure_name and '章' in structure_name:
                return 1  # 章级别
            elif '第' in structure_name and '节' in structure_name:
                return 2  # 节级别
            elif any(keyword in structure_name for keyword in ['声明', '说明', '须知', '注意']):
                return 1  # 声明类
            else:
                return 2  # 默认为2级标题

        return 1

    def _fallback_structure_detection(self, doc: Any) -> dict:
        """
        备用的结构检测方法（使用原有逻辑）

        Args:
            doc: Word文档对象

        Returns:
            文档结构信息
        """
        try:
            logger.info("使用备用结构检测方法")

            # 使用原有的检测逻辑
            outline = []
            for i, paragraph in enumerate(doc.Paragraphs):
                text = paragraph.Range.Text.strip()
                if not text:
                    continue

                style_name = getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal'

                # 检查是否为标题
                is_heading = False
                heading_level = 0
                heading_type = 'unknown'

                # 基于Word样式
                if '标题' in style_name or 'Heading' in style_name:
                    is_heading = True
                    heading_level = self._get_heading_level(style_name)
                    heading_type = 'style'

                # 基于居中对齐
                elif self._is_centered_title(paragraph, text, i + 1):
                    is_heading = True
                    heading_level = self._get_content_heading_level(text)
                    heading_type = 'centered'

                if is_heading:
                    outline_item = {
                        'paragraph_index': i + 1,
                        'text': text,
                        'style': style_name,
                        'level': heading_level,
                        'type': heading_type,
                        'alignment': self._get_paragraph_alignment(paragraph)
                    }
                    outline.append(outline_item)

            # 🔥 优化：直接生成增强的document_structures，移除outline
            document_structures = []
            for item in outline:
                structure_name = item['text'][:50] + ('...' if len(item['text']) > 50 else '')

                # 🔥 新增：为备用方法生成的结构添加count字段
                # 简单的字数统计（基于文本长度）
                text = item['text']
                word_count = self._count_words_in_text(text)

                if '参考文献' in structure_name:
                    # 参考文献：尝试分析条数
                    import re
                    ref_count = len(re.findall(r'\[\d+\]', text))
                    if ref_count > 0:
                        count_display = f"{ref_count}条"
                    else:
                        count_display = "0条"
                else:
                    # 其他结构：使用字数
                    count_display = f"{word_count}字"

                structure = {
                    'name': structure_name,
                    'type': 'detected',
                    'status': 'present',
                    'page': 1,  # 备用方法无法准确获取页码
                    'level': item['level'],  # 🔥 添加level字段到顶级
                    'count': count_display,  # 🔥 新增：统一的count字段
                    'content': {
                        'text': item['text'],
                        'style': item['style'],
                        'level': item['level'],
                        'paragraph_index': item['paragraph_index'],
                        'alignment': item.get('alignment', 'left'),
                        'structure_name': structure_name
                    }
                }
                document_structures.append(structure)

            logger.info(f"备用方法生成了 {len(document_structures)} 个结构，已添加count字段")

            return {
                # 🔥 移除outline，统一使用document_structures
                'document_structures': document_structures,
                'structure_analysis_method': 'fallback'
            }

        except Exception as e:
            logger.error(f"备用结构检测失败: {str(e)}")
            return {
                # 🔥 移除outline，统一使用document_structures
                'document_structures': [],
                'structure_analysis_method': 'failed'
            }

    def _extract_complete_structure_content(self, all_structures: list, pages_content: dict) -> None:
        """
        提取完整的结构内容（从标题段落开始到下一个标题段落之前的所有内容）

        Args:
            all_structures: 所有检测到的结构列表
            pages_content: 页面内容字典
        """
        logger.info("🔥 开始提取完整结构内容")

        # 按页面和段落索引排序所有结构
        sorted_structures = sorted(all_structures, key=lambda x: (
            x.get('page', 0),
            x.get('content', {}).get('paragraph_index', 0)
        ))

        # 为每个结构提取完整内容
        for i, structure in enumerate(sorted_structures):
            if structure.get('status') != 'present':
                continue

            structure_name = structure.get('name', '')
            structure_page = structure.get('page')
            structure_content = structure.get('content', {})
            start_paragraph_index = structure_content.get('paragraph_index', 0)

            # 跳过参考文献结构（已经有专门的处理逻辑）
            if structure_name == '参考文献':
                logger.debug(f"跳过参考文献结构的内容提取")
                continue

            logger.info(f"🔍 提取结构 '{structure_name}' 的完整内容 (第{structure_page}页)")

            # 确定结构边界
            end_page, end_paragraph_index = self._find_structure_boundary(
                structure, sorted_structures, i, pages_content
            )

            # 提取完整内容
            complete_content = self._extract_content_between_boundaries(
                pages_content, structure_page, start_paragraph_index,
                end_page, end_paragraph_index
            )

            # 更新结构的内容信息
            if complete_content:
                structure['content']['complete_text'] = complete_content['text']
                structure['content']['paragraph_count'] = complete_content['paragraph_count']
                structure['content']['word_count'] = complete_content['word_count']

                # 更新count字段为字数统计
                structure['count'] = f"{complete_content['word_count']}字"

                logger.info(f"✅ 结构 '{structure_name}' 完整内容提取完成: "
                          f"{complete_content['word_count']}字, {complete_content['paragraph_count']}段")
            else:
                logger.warning(f"⚠️ 结构 '{structure_name}' 内容提取失败")

        logger.info("🔥 完整结构内容提取完成")

    def _find_structure_boundary(self, current_structure: dict, sorted_structures: list,
                                current_index: int, pages_content: dict) -> tuple:
        """
        查找结构的边界（下一个结构的开始位置）

        Args:
            current_structure: 当前结构
            sorted_structures: 排序后的所有结构
            current_index: 当前结构在列表中的索引
            pages_content: 页面内容字典

        Returns:
            tuple: (结束页面, 结束段落索引)
        """
        current_page = current_structure.get('page')
        current_paragraph_index = current_structure.get('content', {}).get('paragraph_index', 0)

        # 查找下一个结构
        next_structure = None
        for i in range(current_index + 1, len(sorted_structures)):
            candidate = sorted_structures[i]
            if candidate.get('status') == 'present':
                next_structure = candidate
                break

        if next_structure:
            # 如果有下一个结构，边界就是下一个结构的开始位置
            next_page = next_structure.get('page')
            next_paragraph_index = next_structure.get('content', {}).get('paragraph_index', 0)

            # 边界是下一个结构的前一个段落
            if next_page == current_page and next_paragraph_index > 0:
                return next_page, next_paragraph_index - 1
            elif next_page > current_page:
                # 跨页面的情况，返回当前页面的最后一个段落
                current_page_data = pages_content.get(current_page, {})
                current_page_paragraphs = current_page_data.get('paragraphs', [])
                if current_page_paragraphs:
                    return current_page, len(current_page_paragraphs) - 1
                else:
                    return current_page, current_paragraph_index
            else:
                return next_page, max(0, next_paragraph_index - 1)
        else:
            # 如果没有下一个结构，边界就是文档的结尾
            max_page = max(pages_content.keys()) if pages_content else current_page
            max_page_data = pages_content.get(max_page, {})
            max_page_paragraphs = max_page_data.get('paragraphs', [])
            if max_page_paragraphs:
                return max_page, len(max_page_paragraphs) - 1
            else:
                return current_page, current_paragraph_index

    def _extract_content_between_boundaries(self, pages_content: dict, start_page: int,
                                          start_paragraph_index: int, end_page: int,
                                          end_paragraph_index: int) -> dict:
        """
        提取指定边界之间的所有内容

        Args:
            pages_content: 页面内容字典
            start_page: 开始页面
            start_paragraph_index: 开始段落索引
            end_page: 结束页面
            end_paragraph_index: 结束段落索引

        Returns:
            dict: 包含完整文本、段落数和字数的字典
        """
        all_text_parts = []
        paragraph_count = 0

        # 处理单页面情况
        if start_page == end_page:
            page_data = pages_content.get(start_page, {})
            paragraphs = page_data.get('paragraphs', [])

            for i in range(start_paragraph_index, min(end_paragraph_index + 1, len(paragraphs))):
                paragraph = paragraphs[i]
                text = paragraph.get('text', '').strip()
                if text and not paragraph.get('is_in_table', False):  # 跳过表格内容
                    all_text_parts.append(text)
                    paragraph_count += 1
        else:
            # 处理跨页面情况
            for page_num in range(start_page, end_page + 1):
                page_data = pages_content.get(page_num, {})
                paragraphs = page_data.get('paragraphs', [])

                if page_num == start_page:
                    # 第一页：从开始段落到页面结尾
                    for i in range(start_paragraph_index, len(paragraphs)):
                        paragraph = paragraphs[i]
                        text = paragraph.get('text', '').strip()
                        if text and not paragraph.get('is_in_table', False):
                            all_text_parts.append(text)
                            paragraph_count += 1
                elif page_num == end_page:
                    # 最后一页：从页面开始到结束段落
                    for i in range(0, min(end_paragraph_index + 1, len(paragraphs))):
                        paragraph = paragraphs[i]
                        text = paragraph.get('text', '').strip()
                        if text and not paragraph.get('is_in_table', False):
                            all_text_parts.append(text)
                            paragraph_count += 1
                else:
                    # 中间页面：全部内容
                    for paragraph in paragraphs:
                        text = paragraph.get('text', '').strip()
                        if text and not paragraph.get('is_in_table', False):
                            all_text_parts.append(text)
                            paragraph_count += 1

        # 合并所有文本
        complete_text = '\n'.join(all_text_parts)
        word_count = self._count_words_in_text(complete_text)

        return {
            'text': complete_text,
            'paragraph_count': paragraph_count,
            'word_count': word_count
        }


# 全局文档处理器实例
_document_processor = None
_processor_lock = threading.Lock()


def get_document_processor() -> DocumentProcessor:
    """
    获取全局文档处理器实例
    
    Returns:
        DocumentProcessor: 文档处理器实例
    """
    global _document_processor
    
    with _processor_lock:
        if _document_processor is None:
            use_pool = getattr(settings, 'USE_WORD_POOL', True)
            _document_processor = DocumentProcessor(use_pool=use_pool)
        
        return _document_processor


def cleanup_document_processor():
    """清理全局文档处理器实例"""
    global _document_processor

    with _processor_lock:
        if _document_processor is not None:
            _document_processor = None
            logger.info("全局文档处理器实例已清理")