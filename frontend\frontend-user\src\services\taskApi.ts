import apiService from './api'
import type { Task, PaginationParams, PaginatedResponse, TaskProgress, ApiResponse } from '@/types'

export interface TaskListParams extends PaginationParams {
  status?: 'pending' | 'processing' | 'running' | 'completed' | 'failed'
  type?: 'analysis' | 'check' | 'format'
  document_id?: number
  sort_by?: 'created_at' | 'progress' | 'completed_at'
  sort_order?: 'asc' | 'desc'
}

export interface TaskStep {
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  started_at?: string
  completed_at?: string
  error_message?: string
}

export interface TaskDetail extends Task {
  steps?: TaskStep[]
  options?: Record<string, any>
  log_entries?: Array<{
    timestamp: string
    level: 'info' | 'warning' | 'error'
    message: string
  }>
}

export class TaskApi {
  /**
   * 获取任务列表
   */
  async getTasks(params?: TaskListParams): Promise<PaginatedResponse<Task>> {
    const queryParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const url = `/v1/tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return apiService.get<PaginatedResponse<Task>>(url, undefined, {
      retries: 2,
      retryDelay: 500
    })
  }

  /**
   * 获取任务详情
   */
  async getTask(taskId: string): Promise<TaskDetail> {
    return apiService.get<TaskDetail>(`/v1/tasks/${taskId}`)
  }

  /**
   * 获取文档结构统计
   */
  async getDocumentStructureStats(taskId: string): Promise<any> {
    return apiService.get<any>(`/v1/tasks/${taskId}/structure-stats`)
  }

  /**
   * 获取任务状态和进度
   */
  async getTaskStatus(taskId: string): Promise<TaskProgress> {
    return apiService.get<TaskProgress>(`/v1/tasks/${taskId}/status`)
  }

  /**
   * 获取任务详细进度信息（包含步骤状态）
   */
  async getTaskProgress(taskId: string): Promise<any> {
    return apiService.get(`/v1/tasks/${taskId}/progress`)
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<any> {
    return apiService.post(`/v1/tasks/${taskId}/cancel`, {})
  }

  /**
   * 重试失败的任务
   */
  async retryTask(taskId: string): Promise<{ task_id: string }> {
    return apiService.post<{ task_id: string }>(`/v1/tasks/${taskId}/retry`)
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<void> {
    return apiService.delete(`/v1/tasks/${taskId}`)
  }

  /**
   * 批量删除任务
   */
  async deleteTasks(taskIds: string[]): Promise<{
    deleted_count: number
    failed_count: number
    failed_tasks: Array<{ task_id: string; reason: string }>
  }> {
    return apiService.post('/v1/tasks/batch-delete', taskIds)
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(): Promise<{
    total_tasks: number
    pending_tasks: number
    running_tasks: number
    completed_tasks: number
    failed_tasks: number
    average_duration: number
  }> {
    return apiService.get<{
      total_tasks: number
      pending_tasks: number
      running_tasks: number
      completed_tasks: number
      failed_tasks: number
      average_duration: number
    }>('/v1/tasks/stats')
  }

  /**
   * 获取当前用户的活跃任务
   */
  async getActiveTasks(): Promise<Task[]> {
    return apiService.get<Task[]>('/v1/tasks/active')
  }

  /**
   * 获取任务日志
   */
  async getTaskLogs(
    taskId: string,
    level?: 'info' | 'warning' | 'error'
  ): Promise<Array<{
    timestamp: string
    level: 'info' | 'warning' | 'error'
    message: string
    details?: any
  }>> {
    const url = `/v1/tasks/${taskId}/logs${level ? `?level=${level}` : ''}`
    return apiService.get(url)
  }

  /**
   * 清理已完成的任务
   */
  async cleanupCompletedTasks(olderThanDays: number = 30): Promise<{
    deleted_count: number
  }> {
    return apiService.post<{ deleted_count: number }>('/v1/tasks/cleanup', {
      older_than_days: olderThanDays
    })
  }

  /**
   * 导出任务数据
   */
  async exportTasks(
    format: 'csv' | 'json' | 'excel' = 'csv',
    filters?: {
      status?: string
      type?: string
      date_from?: string
      date_to?: string
    }
  ): Promise<void> {
    const params = {
      format,
      ...filters
    }
    
    const queryParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })

    const filename = `tasks_export_${new Date().toISOString().split('T')[0]}.${format}`
    return apiService.download(`/v1/tasks/export?${queryParams.toString()}`, filename)
  }

  /**
   * 设置任务优先级
   */
  async setTaskPriority(taskId: string, priority: 'low' | 'normal' | 'high'): Promise<void> {
    return apiService.put(`/v1/tasks/${taskId}/priority`, { priority })
  }

  /**
   * 轮询任务状态
   * 返回一个可以取消的轮询控制器
   */
  pollTaskStatus(
    taskId: string,
    onUpdate: (status: TaskProgress) => void,
    onError?: (error: any) => void,
    intervalMs: number = 2000
  ): { cancel: () => void } {
    let timer: number | null = null
    let cancelled = false

    const poll = async () => {
      if (cancelled) return

      try {
        const status = await this.getTaskStatus(taskId)
        onUpdate(status)

        // 如果任务还在进行中，继续轮询
        if (status.status === 'pending' || status.status === 'running' || status.status === 'processing') {
          timer = setTimeout(poll, intervalMs)
        }
      } catch (error) {
        if (onError && !cancelled) {
          onError(error)
        }
        // 即使出错也继续轮询，但增加间隔
        if (!cancelled) {
          timer = setTimeout(poll, intervalMs * 2)
        }
      }
    }

    // 立即开始第一次轮询
    poll()

    return {
      cancel: () => {
        cancelled = true
        if (timer) {
          clearTimeout(timer)
        }
      }
    }
  }

  /**
   * 批量轮询多个任务状态
   */
  pollMultipleTasksStatus(
    taskIds: string[],
    onUpdate: (statuses: Record<string, TaskProgress>) => void,
    onError?: (error: any) => void,
    intervalMs: number = 3000
  ): { cancel: () => void } {
    let cancelled = false
    let timer: number | null = null

    const poll = async () => {
      if (cancelled) return

      try {
        const statusPromises = taskIds.map(async (taskId) => {
          try {
            const status = await this.getTaskStatus(taskId)
            return { taskId, status }
          } catch (error) {
            return { taskId, error }
          }
        })

        const results = await Promise.all(statusPromises)
        const statuses: Record<string, TaskProgress> = {}
        let hasRunningTasks = false

        results.forEach(({ taskId, status, error }) => {
          if (status && !error) {
            statuses[taskId] = status
            if (status.status === 'pending' || status.status === 'running' || status.status === 'processing') {
              hasRunningTasks = true
            }
          }
        })

        onUpdate(statuses)

        // 如果还有任务在运行，继续轮询
        if (hasRunningTasks) {
          timer = setTimeout(poll, intervalMs)
        }
      } catch (error) {
        if (onError && !cancelled) {
          onError(error)
        }
        if (!cancelled) {
          timer = setTimeout(poll, intervalMs * 2)
        }
      }
    }

    // 立即开始第一次轮询
    poll()

    return {
      cancel: () => {
        cancelled = true
        if (timer) {
          clearTimeout(timer)
        }
      }
    }
  }

  async getOngoingTasks(limit: number = 5): Promise<Task[]> {
    try {
      const response = await apiService.get<{tasks: Task[], pagination: any}>('/v1/tasks/', {
        params: {
          status: 'processing',
          limit: limit,
          page: 1
        }
      })
      return response.tasks || []
    } catch (error) {
      console.error('获取进行中任务失败:', error)
      return []
    }
  }
}

export const taskApi = new TaskApi()
export default taskApi 