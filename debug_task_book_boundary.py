#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def debug_task_book_boundary():
    """调试任务书边界检测"""
    
    processor = DocumentProcessor()
    
    # 测试文件路径
    test_file = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 开始调试任务书边界检测...")
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(test_file)
        
        # 检查所有结构的顺序
        if 'document_structures' in result:
            structures = result['document_structures']
            
            print(f"\n📋 所有结构列表 (共{len(structures)}个):")
            for i, structure in enumerate(structures):
                name = structure.get('name', '')
                page = structure.get('page', 'N/A')
                status = structure.get('status', 'N/A')
                content = structure.get('content', {})
                paragraph_index = content.get('paragraph_index', 'N/A')
                
                print(f"   {i+1:2d}. {name:20s} | 第{page}页 | 段落索引{paragraph_index} | {status}")
                
                # 特别标记任务书
                if name == '任务书':
                    print(f"       ⭐ 任务书结构 - 索引{i}")
            
            # 找到任务书结构
            task_book_index = None
            for i, structure in enumerate(structures):
                if structure.get('name') == '任务书':
                    task_book_index = i
                    break
            
            if task_book_index is not None:
                task_book = structures[task_book_index]
                print(f"\n🔍 任务书结构详情:")
                print(f"   索引: {task_book_index}")
                print(f"   页面: 第{task_book.get('page')}页")
                
                content = task_book.get('content', {})
                print(f"   段落索引: {content.get('paragraph_index')}")
                print(f"   原始文本: {content.get('text', '')[:100]}...")
                
                # 检查下一个结构
                if task_book_index + 1 < len(structures):
                    next_structure = structures[task_book_index + 1]
                    print(f"\n📍 下一个结构:")
                    print(f"   名称: {next_structure.get('name')}")
                    print(f"   页面: 第{next_structure.get('page')}页")
                    next_content = next_structure.get('content', {})
                    print(f"   段落索引: {next_content.get('paragraph_index')}")
                    print(f"   文本: {next_content.get('text', '')[:100]}...")
                else:
                    print(f"\n📍 任务书是最后一个结构")
                
                # 检查第2页的所有段落和表格
                if 'content' in result:
                    paragraphs = result['content'].get('paragraphs', [])
                    tables = result['content'].get('tables', [])
                    
                    # 第2页的段落
                    page_2_paragraphs = [p for p in paragraphs if p.get('page_number') == 2]
                    page_2_tables = [t for t in tables if t.get('page_number') == 2]
                    
                    print(f"\n📄 第2页内容统计:")
                    print(f"   段落数: {len(page_2_paragraphs)}")
                    print(f"   表格数: {len(page_2_tables)}")
                    
                    print(f"\n📝 第2页段落索引范围:")
                    if page_2_paragraphs:
                        min_index = min(p.get('index', 0) for p in page_2_paragraphs)
                        max_index = max(p.get('index', 0) for p in page_2_paragraphs)
                        print(f"   段落索引范围: {min_index} - {max_index}")
                    
                    print(f"\n📊 第2页表格索引范围:")
                    if page_2_tables:
                        min_index = min(t.get('index', 0) for t in page_2_tables)
                        max_index = max(t.get('index', 0) for t in page_2_tables)
                        print(f"   表格索引范围: {min_index} - {max_index}")
                    
                    # 显示任务书段落索引附近的内容
                    task_paragraph_index = content.get('paragraph_index', 0)
                    print(f"\n🔍 任务书段落索引{task_paragraph_index}附近的内容:")
                    
                    # 合并段落和表格，按索引排序
                    all_content = []
                    for p in page_2_paragraphs:
                        all_content.append({
                            'type': 'paragraph',
                            'index': p.get('index', 0),
                            'text': p.get('text', ''),
                            'is_in_table': p.get('is_in_table', False)
                        })
                    
                    for t in page_2_tables:
                        all_content.append({
                            'type': 'table',
                            'index': t.get('index', 0),
                            'text': t.get('text', '')
                        })
                    
                    all_content.sort(key=lambda x: x['index'])
                    
                    # 显示任务书索引前后的内容
                    start_range = max(0, task_paragraph_index - 5)
                    end_range = min(len(all_content), task_paragraph_index + 20)
                    
                    for item in all_content[start_range:end_range]:
                        index = item['index']
                        text = item['text'][:50]
                        item_type = item['type']
                        is_in_table = item.get('is_in_table', False)
                        
                        marker = "⭐" if index == task_paragraph_index else "  "
                        table_marker = "[表格]" if item_type == 'table' or is_in_table else "[段落]"
                        
                        print(f"   {marker} 索引{index:3d} {table_marker} | {text}...")
            
            else:
                print("❌ 未找到任务书结构")
        else:
            print("❌ 结果中没有document_structures字段")
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_task_book_boundary()
