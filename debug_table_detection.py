#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def debug_table_detection():
    """调试表格检测功能"""
    
    processor = DocumentProcessor()
    
    # 测试文件路径
    test_file = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 开始调试表格检测...")
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(test_file)
        
        # 检查数据结构
        print(f"🔍 结果的顶级键: {list(result.keys())}")

        if 'content' in result:
            content = result['content']
            print(f"🔍 content的键: {list(content.keys())}")

            if 'pages_content' in content:
                pages_content = content['pages_content']
            
            print(f"\n📋 文档总页数: {len(pages_content)}")
            
            # 检查第2页的详细信息
            page_2 = pages_content.get(2, {})
            paragraphs = page_2.get('paragraphs', [])
            tables = page_2.get('tables', [])
            
            print(f"\n🔍 第2页详细信息:")
            print(f"   段落数: {len(paragraphs)}")
            print(f"   表格数: {len(tables)}")
            
            print(f"\n📝 第2页段落详情:")
            for i, para in enumerate(paragraphs):
                text = para.get('text', '').strip()
                is_in_table = para.get('is_in_table', False)
                index = para.get('index', 'N/A')
                
                if text:
                    print(f"   段落{i+1:2d} (索引{index}): {'[表格]' if is_in_table else '[普通]'} | {text[:100]}...")
            
            print(f"\n📊 第2页表格详情:")
            for i, table in enumerate(tables):
                text = table.get('text', '').strip()
                index = table.get('index', 'N/A')
                
                if text:
                    print(f"   表格{i+1:2d} (索引{index}): | {text[:100]}...")
            
            # 检查所有页面的表格内容
            print(f"\n🔍 所有页面的表格统计:")
            total_table_items = 0
            for page_num, page_data in pages_content.items():
                page_tables = page_data.get('tables', [])
                if page_tables:
                    print(f"   第{page_num}页: {len(page_tables)}个表格项")
                    total_table_items += len(page_tables)
                    
                    # 显示前3个表格项的内容
                    for i, table_item in enumerate(page_tables[:3]):
                        text = table_item.get('text', '').strip()
                        if text:
                            print(f"     表格{i+1}: {text[:80]}...")
            
            print(f"\n📊 总表格项数: {total_table_items}")
            
            # 检查任务书关键词在哪些段落中
            task_keywords = ['学生姓名', '专业名称', '指导教师', '设计题目', '研究内容', '研究方法', '任务书']
            print(f"\n🔍 搜索任务书关键词:")
            
            found_in_paragraphs = []
            found_in_tables = []
            
            for page_num, page_data in pages_content.items():
                # 检查段落
                for para in page_data.get('paragraphs', []):
                    text = para.get('text', '')
                    for keyword in task_keywords:
                        if keyword in text:
                            found_in_paragraphs.append((page_num, para, keyword))
                
                # 检查表格
                for table in page_data.get('tables', []):
                    text = table.get('text', '')
                    for keyword in task_keywords:
                        if keyword in text:
                            found_in_tables.append((page_num, table, keyword))
            
            print(f"   在段落中找到: {len(found_in_paragraphs)}个匹配")
            for page_num, para, keyword in found_in_paragraphs:
                text = para.get('text', '').strip()
                is_in_table = para.get('is_in_table', False)
                print(f"     第{page_num}页 {'[表格段落]' if is_in_table else '[普通段落]'} 关键词'{keyword}': {text[:60]}...")
            
            print(f"   在表格中找到: {len(found_in_tables)}个匹配")
            for page_num, table, keyword in found_in_tables:
                text = table.get('text', '').strip()
                print(f"     第{page_num}页 [表格项] 关键词'{keyword}': {text[:60]}...")
                
            else:
                print("❌ content中没有pages_content")
        else:
            print("❌ 结果中没有content字段")
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_table_detection()
