#!/usr/bin/env python3
"""
检查第2页的原始段落数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.services.document_processor import DocumentProcessor

def check_page2_content():
    try:
        processor = DocumentProcessor()
        
        # 使用正确的文件路径
        doc_path = os.path.abspath('docs/test.docx')

        result = processor.analyze_document_comprehensive(doc_path, include_formatting=True)

        print(f"🔍 结果结构的顶级键: {list(result.keys())}")

        if 'pages_content' in result:
            page_2_content = result['pages_content'].get(2, {})
            paragraphs = page_2_content.get('paragraphs', [])

            print(f"🔍 第2页原始段落数据:")
            print(f"   总段落数: {len(paragraphs)}")
            print(f"   页面信息: {page_2_content.get('page_number', 'N/A')}")

            for i, para in enumerate(paragraphs):
                text = para.get('text', '').strip()
                is_in_table = para.get('is_in_table', False)
                font_size = para.get('font_size', 'N/A')
                is_bold = para.get('is_bold', False)
                alignment = para.get('alignment', 'N/A')

                if text:
                    print(f"   段落{i+1:2d}: {'[表格]' if is_in_table else '[普通]'} {font_size}pt {'粗体' if is_bold else '普通'} {alignment} | {text[:80]}...")

            # 检查表格关键词
            all_text = ' '.join([p.get('text', '') for p in paragraphs])
            table_keywords = ['学生姓名', '专业名称', '指导教师', '设计题目', '研究内容', '研究方法', '任务书']
            found_keywords = [kw for kw in table_keywords if kw in all_text]
            print(f"\n   页面包含的表格关键词: {found_keywords}")
            print(f"   页面总字符数: {len(all_text)}")

        elif 'content' in result:
            print("🔍 在content字段中查找数据")
            content = result['content']
            print(f"   content字段的键: {list(content.keys())}")

            if 'pages_content' in content:
                print("🔍 在content字段中找到pages_content")
                page_2_content = content['pages_content'].get(2, {})
                paragraphs = page_2_content.get('paragraphs', [])

                print(f"🔍 第2页原始段落数据:")
                print(f"   总段落数: {len(paragraphs)}")
                print(f"   页面信息: {page_2_content.get('page_number', 'N/A')}")

                for i, para in enumerate(paragraphs):
                    text = para.get('text', '').strip()
                    is_in_table = para.get('is_in_table', False)
                    font_size = para.get('font_size', 'N/A')
                    is_bold = para.get('is_bold', False)
                    alignment = para.get('alignment', 'N/A')

                    if text:
                        print(f"   段落{i+1:2d}: {'[表格]' if is_in_table else '[普通]'} {font_size}pt {'粗体' if is_bold else '普通'} {alignment} | {text[:80]}...")

                # 检查表格关键词
                all_text = ' '.join([p.get('text', '') for p in paragraphs])
                table_keywords = ['学生姓名', '专业名称', '指导教师', '设计题目', '研究内容', '研究方法', '任务书']
                found_keywords = [kw for kw in table_keywords if kw in all_text]
                print(f"\n   页面包含的表格关键词: {found_keywords}")
                print(f"   页面总字符数: {len(all_text)}")
            elif 'paragraphs' in content:
                print("🔍 在content字段中找到paragraphs")
                paragraphs = content['paragraphs']

                print(f"🔍 所有段落数据:")
                print(f"   总段落数: {len(paragraphs)}")

                # 查找包含任务书关键词的段落
                task_book_paragraphs = []
                table_keywords = ['学生姓名', '专业名称', '指导教师', '设计题目', '研究内容', '研究方法', '任务书']

                for i, para in enumerate(paragraphs):
                    text = para.get('text', '').strip()
                    is_in_table = para.get('is_in_table', False)
                    page_number = para.get('page_number', 'N/A')

                    if text and any(keyword in text for keyword in table_keywords):
                        task_book_paragraphs.append((i, para))
                        print(f"   段落{i+1:2d}: 第{page_number}页 {'[表格]' if is_in_table else '[普通]'} | {text[:80]}...")

                print(f"\n   找到包含任务书关键词的段落数: {len(task_book_paragraphs)}")

                # 检查第2页的所有段落
                page_2_paragraphs = [p for p in paragraphs if p.get('page_number') == 2]
                print(f"\n🔍 第2页段落详情:")
                print(f"   第2页总段落数: {len(page_2_paragraphs)}")

                for i, para in enumerate(page_2_paragraphs):
                    text = para.get('text', '').strip()
                    is_in_table = para.get('is_in_table', False)
                    font_size = para.get('font_size', 'N/A')
                    is_bold = para.get('is_bold', False)
                    alignment = para.get('alignment', 'N/A')

                    if text:
                        print(f"   段落{i+1:2d}: {'[表格]' if is_in_table else '[普通]'} {font_size}pt {'粗体' if is_bold else '普通'} {alignment} | {text[:80]}...")

                # 检查表格关键词
                all_page2_text = ' '.join([p.get('text', '') for p in page_2_paragraphs])
                found_keywords = [kw for kw in table_keywords if kw in all_page2_text]
                print(f"\n   第2页包含的表格关键词: {found_keywords}")
                print(f"   第2页总字符数: {len(all_page2_text)}")
            else:
                print("❌ content字段中未找到pages_content或paragraphs")

        else:
            print("❌ 未找到页面内容数据")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_page2_content()
