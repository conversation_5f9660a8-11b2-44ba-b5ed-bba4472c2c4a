#!/usr/bin/env python3
"""
调试结构检测的页面分配问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_structure_detection():
    """调试结构检测"""
    doc_path = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        return
    
    try:
        # 创建文档处理器
        processor = DocumentProcessor()

        # 打开文档
        print("📂 正在打开文档...")
        from app.core.resource_manager import WordInstancePool
        word_pool = WordInstancePool()

        with word_pool.get_instance() as word_instance:
            doc = word_instance.word_app.Documents.Open(doc_path)

            try:
                # 直接调用结构检测方法
                print("🔍 开始结构检测...")
                result = processor._detect_document_structure_by_rules(doc)

            finally:
                # 关闭文档
                doc.Close()
        
        if 'document_structures' in result:
            structures = result['document_structures']
            print(f"📊 检测到 {len(structures)} 个结构")
            
            # 分析页面分布
            page_counts = {}
            for structure in structures:
                page = structure.get('page', 'N/A')
                if page not in page_counts:
                    page_counts[page] = 0
                page_counts[page] += 1

            print(f"\n📋 页面分布:")
            # 分别处理数字页面和非数字页面
            numeric_pages = [p for p in page_counts.keys() if isinstance(p, int)]
            non_numeric_pages = [p for p in page_counts.keys() if not isinstance(p, int)]

            for page in sorted(numeric_pages):
                print(f"第 {page:2d} 页: {page_counts[page]} 个结构")
            for page in non_numeric_pages:
                print(f"第 {page} 页: {page_counts[page]} 个结构")

            # 显示前10个结构的详细信息
            print(f"\n📋 前10个结构详情:")
            for i, structure in enumerate(structures[:10]):
                name = structure.get('name', 'N/A')
                page = structure.get('page', 'N/A')
                struct_type = structure.get('type', 'N/A')
                content_text = structure.get('content', {}).get('text', '')[:30]
                word_count = structure.get('content', {}).get('word_count', 0)

                print(f"{i+1:2d}. {name:20s} | 第{page}页 | {struct_type:10s} | {word_count}字 | {content_text}...")

            # 🔥 特别检查任务书的详细内容
            task_book = next((s for s in structures if s['name'] == '任务书'), None)
            if task_book:
                print(f"\n🔍 任务书详细信息:")
                print(f"   页面: 第{task_book.get('page')}页")
                print(f"   字数: {task_book.get('content', {}).get('word_count', 0)}字")
                print(f"   段落数: {task_book.get('content', {}).get('paragraph_count', 0)}段")
                full_text = task_book.get('content', {}).get('text', '')
                print(f"   完整内容长度: {len(full_text)}字符")
                print(f"   内容预览: {full_text[:300]}...")

                # 检查是否包含表格关键词
                table_keywords = ['学生姓名', '专业名称', '指导教师', '设计题目', '研究内容', '研究方法']
                found_keywords = [kw for kw in table_keywords if kw in full_text]
                print(f"   包含表格关键词: {found_keywords}")
                print(f"   是否包含表格内容: {'是' if found_keywords else '否'}")

                # 🔥 检查第2页的原始段落数据
                print(f"\n🔍 检查第2页原始段落数据:")
                try:
                    # 重新解析文档以获取原始数据
                    processor_new = DocumentProcessor()
                    with open('test_files/test_document.pdf', 'rb') as f:
                        result_new = processor_new.process_document(f.read(), 'test_document.pdf')
                        if 'pages_content' in result_new:
                            page_2_content = result_new['pages_content'].get(2, {})
                            paragraphs = page_2_content.get('paragraphs', [])
                            print(f"   第2页总段落数: {len(paragraphs)}")
                            for i, para in enumerate(paragraphs):
                                text = para.get('text', '').strip()
                                is_in_table = para.get('is_in_table', False)
                                if text:
                                    print(f"   段落{i+1}: {'[表格]' if is_in_table else '[普通]'} {text[:50]}...")
                except Exception as e:
                    print(f"   检查失败: {e}")
        
        else:
            print("❌ 未找到结构检测结果")
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_structure_detection()
