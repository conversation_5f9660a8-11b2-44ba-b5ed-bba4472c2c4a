#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def debug_table_detection():
    """调试表格检测功能"""
    
    processor = DocumentProcessor()
    
    # 测试文件路径
    test_file = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 开始调试表格检测...")
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(test_file)
        
        # 检查数据结构
        print(f"🔍 结果的顶级键: {list(result.keys())}")
        
        if 'content' in result:
            content = result['content']
            print(f"🔍 content的键: {list(content.keys())}")
            
            # 新的数据结构：直接从content中获取paragraphs和tables
            paragraphs = content.get('paragraphs', [])
            tables = content.get('tables', [])
            
            print(f"\n📋 文档内容统计:")
            print(f"   总段落数: {len(paragraphs)}")
            print(f"   总表格数: {len(tables)}")
            
            # 检查任务书关键词在哪些段落中
            task_keywords = ['学生姓名', '专业名称', '指导教师', '设计题目', '研究内容', '研究方法', '任务书']
            print(f"\n🔍 搜索任务书关键词:")
            
            found_in_paragraphs = []
            found_in_tables = []
            
            # 检查段落
            for i, para in enumerate(paragraphs):
                text = para.get('text', '')
                page_number = para.get('page_number', 'N/A')
                is_in_table = para.get('is_in_table', False)
                
                for keyword in task_keywords:
                    if keyword in text:
                        found_in_paragraphs.append((i, para, keyword))
            
            # 检查表格
            for i, table in enumerate(tables):
                text = table.get('text', '')
                page_number = table.get('page_number', 'N/A')
                
                for keyword in task_keywords:
                    if keyword in text:
                        found_in_tables.append((i, table, keyword))
            
            print(f"   在段落中找到: {len(found_in_paragraphs)}个匹配")
            for i, para, keyword in found_in_paragraphs:
                text = para.get('text', '').strip()
                page_number = para.get('page_number', 'N/A')
                is_in_table = para.get('is_in_table', False)
                print(f"     段落{i+1} 第{page_number}页 {'[表格段落]' if is_in_table else '[普通段落]'} 关键词'{keyword}': {text[:60]}...")
            
            print(f"   在表格中找到: {len(found_in_tables)}个匹配")
            for i, table, keyword in found_in_tables:
                text = table.get('text', '').strip()
                page_number = table.get('page_number', 'N/A')
                print(f"     表格{i+1} 第{page_number}页 [表格项] 关键词'{keyword}': {text[:60]}...")
            
            # 检查第2页的所有内容
            print(f"\n🔍 第2页内容详情:")
            page_2_paragraphs = [p for p in paragraphs if p.get('page_number') == 2]
            page_2_tables = [t for t in tables if t.get('page_number') == 2]
            
            print(f"   第2页段落数: {len(page_2_paragraphs)}")
            print(f"   第2页表格数: {len(page_2_tables)}")
            
            print(f"\n📝 第2页段落详情:")
            for i, para in enumerate(page_2_paragraphs):
                text = para.get('text', '').strip()
                is_in_table = para.get('is_in_table', False)
                index = para.get('index', 'N/A')
                
                if text:
                    print(f"   段落{i+1:2d} (索引{index}): {'[表格]' if is_in_table else '[普通]'} | {text[:100]}...")
            
            print(f"\n📊 第2页表格详情:")
            for i, table in enumerate(page_2_tables):
                text = table.get('text', '').strip()
                index = table.get('index', 'N/A')
                
                if text:
                    print(f"   表格{i+1:2d} (索引{index}): | {text[:100]}...")
            
            # 检查任务书结构的内容
            if 'document_structures' in result:
                structures = result['document_structures']
                print(f"\n🔍 文档结构信息:")
                
                for structure in structures:
                    name = structure.get('name', '')
                    if '任务书' in name:
                        print(f"   找到任务书结构: {name}")
                        print(f"     页面: {structure.get('page', 'N/A')}")
                        print(f"     字数: {structure.get('word_count', 'N/A')}")
                        print(f"     段落数: {structure.get('paragraph_count', 'N/A')}")
                        
                        # 检查完整内容
                        complete_content = structure.get('complete_content', '')
                        if complete_content:
                            print(f"     完整内容长度: {len(complete_content)}")
                            print(f"     完整内容预览: {complete_content[:200]}...")
                        else:
                            print(f"     完整内容: 无")
                            
        else:
            print("❌ 结果中没有content字段")
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_table_detection()
