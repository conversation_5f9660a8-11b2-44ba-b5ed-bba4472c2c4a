#!/usr/bin/env python3
"""
分析test.docx文档中的非标准结构
"""

import win32com.client
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_potential_non_standard_structures():
    """分析潜在的非标准结构"""
    doc_path = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        return
    
    try:
        # 启动Word应用
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        
        # 打开文档
        doc = word_app.Documents.Open(doc_path)
        
        print(f"📄 文档总页数: {doc.Range().Information(4)}")
        print(f"📄 文档总段落数: {len(doc.Paragraphs)}")
        
        # 分析所有段落，寻找潜在的非标准结构
        print("\n🔍 潜在非标准结构分析:")
        print("-" * 80)
        
        potential_structures = []
        
        for i in range(len(doc.Paragraphs)):
            paragraph = doc.Paragraphs(i + 1)
            
            try:
                text = paragraph.Range.Text.strip()
                if not text or len(text) < 2:
                    continue
                
                # 获取段落属性
                page_number = 1
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Information'):
                        page_number = range_obj.Information(3)
                
                # 获取样式
                style = "Normal"
                if hasattr(paragraph, 'Style'):
                    style = getattr(paragraph.Style, 'NameLocal', 'Normal')
                
                # 获取格式信息
                is_bold = False
                font_size = 12
                alignment = 'left'
                
                if hasattr(paragraph, 'Range') and hasattr(paragraph.Range, 'Font'):
                    is_bold = getattr(paragraph.Range.Font, 'Bold', False)
                    font_size = getattr(paragraph.Range.Font, 'Size', 12)
                
                if hasattr(paragraph, 'Format'):
                    alignment_value = getattr(paragraph.Format, 'Alignment', 0)
                    alignment = 'center' if alignment_value == 1 else 'left'
                
                # 检查是否可能是非标准结构
                is_potential_structure = False
                reasons = []
                
                # 条件1：居中对齐
                if alignment == 'center':
                    is_potential_structure = True
                    reasons.append("居中对齐")
                
                # 条件2：粗体
                if is_bold:
                    is_potential_structure = True
                    reasons.append("粗体")
                
                # 条件3：大字号
                if font_size >= 14:
                    is_potential_structure = True
                    reasons.append(f"大字号({font_size}pt)")
                
                # 条件4：特殊样式
                if style and style != 'Normal' and '标题' in style:
                    is_potential_structure = True
                    reasons.append(f"标题样式({style})")
                
                # 条件5：短文本且包含特殊字符
                if len(text) <= 50 and any(char in text for char in ['：', '、', '（', '）', '【', '】']):
                    is_potential_structure = True
                    reasons.append("短文本+特殊字符")
                
                # 排除明显的正文内容
                if len(text) > 100:
                    is_potential_structure = False
                    reasons = ["文本过长，可能是正文"]
                
                # 排除标准结构
                standard_keywords = [
                    '摘要', 'abstract', '关键词', 'key words', '目录', '绪论', '引言',
                    '参考文献', '致谢', '附录', '结论', '总结'
                ]
                
                if any(keyword in text.lower() for keyword in standard_keywords):
                    is_potential_structure = False
                    reasons = ["标准结构"]
                
                if is_potential_structure and reasons[0] != "文本过长，可能是正文":
                    potential_structures.append({
                        'text': text,
                        'page': page_number,
                        'style': style,
                        'is_bold': is_bold,
                        'font_size': font_size,
                        'alignment': alignment,
                        'reasons': reasons,
                        'paragraph_index': i
                    })
            
            except Exception as e:
                continue
        
        # 显示潜在的非标准结构
        print(f"📊 找到 {len(potential_structures)} 个潜在非标准结构:")
        print()
        
        for i, structure in enumerate(potential_structures, 1):
            print(f"🔍 潜在结构 {i}:")
            print(f"   页面: 第{structure['page']}页")
            print(f"   文本: {structure['text'][:50]}{'...' if len(structure['text']) > 50 else ''}")
            print(f"   样式: {structure['style']}")
            print(f"   格式: {'粗体' if structure['is_bold'] else '正常'}, {structure['font_size']}pt, {structure['alignment']}")
            print(f"   识别原因: {', '.join(structure['reasons'])}")
            print(f"   段落索引: {structure['paragraph_index']}")
            print("-" * 40)
        
        # 关闭文档
        doc.Close()
        word_app.Quit()
        
        print(f"\n✅ 分析完成，共找到 {len(potential_structures)} 个潜在非标准结构")
        
        return potential_structures
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    analyze_potential_non_standard_structures()
