#!/usr/bin/env python3
"""
测试页面提取是否正确
"""

import win32com.client
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_page_extraction():
    """测试页面提取"""
    doc_path = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        return
    
    try:
        # 启动Word应用
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        
        # 打开文档
        doc = word_app.Documents.Open(doc_path)
        
        print(f"📄 文档总页数: {doc.Range().Information(4)}")  # wdNumberOfPagesInDocument = 4
        print(f"📄 文档总段落数: {len(doc.Paragraphs)}")
        
        # 检查前20个段落的页面分配
        print("\n📋 前20个段落的页面分配:")
        print("-" * 60)

        paragraph_count = min(20, len(doc.Paragraphs))
        for i in range(paragraph_count):
            paragraph = doc.Paragraphs(i + 1)  # COM对象从1开始索引
            try:
                text = paragraph.Range.Text.strip()
                if not text:
                    continue
                
                # 获取页面号
                page_number = 1
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Information'):
                        page_number = range_obj.Information(3)  # wdActiveEndPageNumber = 3
                
                # 获取样式
                style = "Normal"
                if hasattr(paragraph, 'Style'):
                    style = getattr(paragraph.Style, 'NameLocal', 'Normal')
                
                print(f"段落 {i+1:2d}: 页面 {page_number} | {style:20s} | {text[:40]}...")
                
            except Exception as e:
                print(f"段落 {i+1:2d}: 错误 - {str(e)}")
        
        # 检查特定页面的段落分布
        print(f"\n📊 各页面段落分布:")
        print("-" * 40)
        
        page_counts = {}
        for i in range(len(doc.Paragraphs)):
            paragraph = doc.Paragraphs(i + 1)
            try:
                text = paragraph.Range.Text.strip()
                if not text:
                    continue
                
                page_number = 1
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Information'):
                        page_number = range_obj.Information(3)
                
                if page_number not in page_counts:
                    page_counts[page_number] = 0
                page_counts[page_number] += 1
                
            except Exception as e:
                continue
        
        for page_num in sorted(page_counts.keys()):
            print(f"第 {page_num:2d} 页: {page_counts[page_num]:3d} 个段落")
        
        # 查找特定结构的页面位置
        print(f"\n🔍 特定结构的页面位置:")
        print("-" * 50)
        
        target_structures = ["摘　　要", "ABSTRACT", "目　　录", "绪论", "参考文献", "致　　谢"]

        for i in range(len(doc.Paragraphs)):
            paragraph = doc.Paragraphs(i + 1)
            try:
                text = paragraph.Range.Text.strip()
                
                for target in target_structures:
                    if target in text and len(text) <= 10:  # 只匹配短文本（标题）
                        page_number = 1
                        if hasattr(paragraph, 'Range'):
                            range_obj = paragraph.Range
                            if hasattr(range_obj, 'Information'):
                                page_number = range_obj.Information(3)
                        
                        style = "Normal"
                        if hasattr(paragraph, 'Style'):
                            style = getattr(paragraph.Style, 'NameLocal', 'Normal')
                        
                        print(f"{target:10s}: 第 {page_number:2d} 页 | {style:20s} | 段落 {i+1}")
                        break
                
            except Exception as e:
                continue
        
        # 关闭文档
        doc.Close()
        word_app.Quit()
        
        print(f"\n✅ 页面提取测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_page_extraction()
